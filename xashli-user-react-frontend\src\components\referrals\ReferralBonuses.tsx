import { useState } from 'react';
import {
  TrendingUp,
  Banknote,
  Filter,
  User,
  Calendar,
  Coins,
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Button } from '../ui/Button';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '../ui/Select';
import type {
  BonusesApiResponse,
  ReferralBonusesFilters,
} from '../../types/referral';

interface ReferralBonusesProps {
  bonuses: BonusesApiResponse;
  onFilterChange: (filters: ReferralBonusesFilters) => void;
}

export function ReferralBonuses({
  bonuses,
  onFilterChange,
}: ReferralBonusesProps) {
  const [filters, setFilters] = useState<ReferralBonusesFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  const { bonuses: bonusData, summary } = bonuses;

  const formatAmount = (amount: number, currency: 'fiat' | 'crypto') => {
    const num = Number(amount) || 0;
    if (currency === 'fiat') {
      return `₦${num.toFixed(2)}`;
    }
    return `${num.toFixed(8)} SOL`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getLevelBadgeColor = (level: number) => {
    switch (level) {
      case 1:
        return 'bg-blue-500/10 text-blue-600 border-blue-200';
      case 2:
        return 'bg-green-500/10 text-green-600 border-green-200';
      case 3:
        return 'bg-purple-500/10 text-purple-600 border-purple-200';
      default:
        return 'bg-gray-500/10 text-gray-600 border-gray-200';
    }
  };

  const getStatusBadge = (bonus: any) => {
    if (bonus.is_consumed) {
      return (
        <Badge className='bg-gray-500/10 text-gray-600 border-gray-200'>
          Used
        </Badge>
      );
    }
    if (bonus.is_withdrawable) {
      return (
        <Badge className='bg-green-500/10 text-green-600 border-green-200'>
          Available
        </Badge>
      );
    }
    return (
      <Badge className='bg-yellow-500/10 text-yellow-600 border-yellow-200'>
        Pending
      </Badge>
    );
  };

  const handleFilterChange = (
    key: keyof ReferralBonusesFilters,
    value: any
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    onFilterChange({});
  };

  return (
    <div className='space-y-6'>
      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6'>
        {/* Fiat Summary */}
        <Card className='p-4 md:p-6'>
          <div className='flex items-center justify-between mb-3 md:mb-4'>
            <h3 className='text-base md:text-lg font-semibold text-foreground'>
              Fiat Bonuses
            </h3>
            <Banknote className='h-4 w-4 md:h-5 md:w-5 text-green-500 flex-shrink-0' />
          </div>
          <div className='space-y-2 md:space-y-3'>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Total Earned:
              </span>
              <span className='text-xs md:text-sm font-medium'>
                {formatAmount(summary.fiat.total_referral_bonus_amount, 'fiat')}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Available:
              </span>
              <span className='text-xs md:text-sm font-medium text-green-600'>
                {formatAmount(
                  summary.fiat.withdrawable_referral_amount,
                  'fiat'
                )}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Used:
              </span>
              <span className='text-xs md:text-sm font-medium text-gray-600'>
                {formatAmount(summary.fiat.consumed_referral_amount, 'fiat')}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Pending:
              </span>
              <span className='text-xs md:text-sm font-medium text-yellow-600'>
                {formatAmount(summary.fiat.pending_referral_amount, 'fiat')}
              </span>
            </div>
          </div>
        </Card>

        {/* Crypto Summary */}
        <Card className='p-4 md:p-6'>
          <div className='flex items-center justify-between mb-3 md:mb-4'>
            <h3 className='text-base md:text-lg font-semibold text-foreground'>
              Crypto Bonuses
            </h3>
            <Coins className='h-4 w-4 md:h-5 md:w-5 text-orange-500 flex-shrink-0' />
          </div>
          <div className='space-y-2 md:space-y-3'>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Total Earned:
              </span>
              <span className='text-xs md:text-sm font-medium break-all'>
                {formatAmount(
                  summary.crypto.total_referral_bonus_amount,
                  'crypto'
                )}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Available:
              </span>
              <span className='text-xs md:text-sm font-medium text-green-600 break-all'>
                {formatAmount(
                  summary.crypto.withdrawable_referral_amount,
                  'crypto'
                )}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Used:
              </span>
              <span className='text-xs md:text-sm font-medium text-gray-600 break-all'>
                {formatAmount(
                  summary.crypto.consumed_referral_amount,
                  'crypto'
                )}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-xs md:text-sm text-foreground-secondary'>
                Pending:
              </span>
              <span className='text-xs md:text-sm font-medium text-yellow-600 break-all'>
                {formatAmount(summary.crypto.pending_referral_amount, 'crypto')}
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className='p-3 md:p-4'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0'>
          <Button
            variant='outline'
            onClick={() => setShowFilters(!showFilters)}
            className='flex items-center gap-2 text-sm'
            size='sm'
          >
            <Filter className='h-3 w-3 md:h-4 md:w-4' />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
          {Object.keys(filters).length > 0 && (
            <Button
              variant='ghost'
              onClick={clearFilters}
              size='sm'
              className='text-xs md:text-sm'
            >
              Clear Filters
            </Button>
          )}
        </div>

        {showFilters && (
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mt-3 md:mt-4'>
            <div>
              <label className='block text-xs md:text-sm font-medium text-foreground-secondary mb-1 md:mb-2'>
                From Date
              </label>
              <input
                type='date'
                value={filters.date_from || ''}
                onChange={e =>
                  handleFilterChange('date_from', e.target.value || undefined)
                }
                onClick={e => {
                  e.currentTarget.showPicker?.();
                }}
                className='w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary bg-background text-foreground cursor-pointer [&::-webkit-calendar-picker-indicator]:cursor-pointer [&::-webkit-calendar-picker-indicator]:opacity-70'
              />
            </div>

            <div>
              <label className='block text-xs md:text-sm font-medium text-foreground-secondary mb-1 md:mb-2'>
                To Date
              </label>
              <input
                type='date'
                value={filters.date_to || ''}
                onChange={e =>
                  handleFilterChange('date_to', e.target.value || undefined)
                }
                onClick={e => {
                  e.currentTarget.showPicker?.();
                }}
                className='w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary bg-background text-foreground cursor-pointer [&::-webkit-calendar-picker-indicator]:cursor-pointer [&::-webkit-calendar-picker-indicator]:opacity-70'
              />
            </div>

            <div>
              <label className='block text-xs md:text-sm font-medium text-foreground-secondary mb-1 md:mb-2'>
                Level
              </label>
              <Select
                value={filters.level?.toString() || ''}
                onValueChange={value =>
                  handleFilterChange(
                    'level',
                    value ? parseInt(value) : undefined
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='All Levels' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=''>All Levels</SelectItem>
                  <SelectItem value='1'>Level 1</SelectItem>
                  <SelectItem value='2'>Level 2</SelectItem>
                  <SelectItem value='3'>Level 3</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </Card>

      {/* Bonuses List */}
      <Card className='p-3 md:p-6'>
        <h3 className='text-base md:text-lg font-semibold text-foreground mb-3 md:mb-4'>
          Referral Bonuses ({bonusData.length})
        </h3>

        {bonusData.length === 0 ? (
          <div className='text-center py-6 md:py-8'>
            <TrendingUp className='h-8 w-8 md:h-12 md:w-12 text-foreground-muted mx-auto mb-3 md:mb-4' />
            <h4 className='text-base md:text-lg font-medium text-foreground mb-2'>
              No Bonuses Yet
            </h4>
            <p className='text-sm md:text-base text-foreground-secondary'>
              Bonuses will appear here when your referees make successful
              transactions.
            </p>
          </div>
        ) : (
          <div className='space-y-3'>
            {bonusData.map(bonus => (
              <div
                key={bonus.id}
                className='flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 md:p-4 border border-border rounded-lg bg-background hover:bg-background-secondary/50 transition-colors gap-3 sm:gap-4'
              >
                <div className='flex items-center space-x-3'>
                  <div className='h-8 w-8 md:h-10 md:w-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0'>
                    <User className='h-4 w-4 md:h-5 md:w-5 text-primary' />
                  </div>
                  <div className='min-w-0 flex-1'>
                    <p className='font-medium text-foreground text-sm md:text-base truncate'>
                      {bonus.referee_user.full_name}
                    </p>
                    <p className='text-xs md:text-sm text-foreground-secondary truncate'>
                      {bonus.referee_user.email}
                    </p>
                  </div>
                </div>

                <div className='flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4'>
                  <div className='text-left sm:text-right'>
                    <p className='font-medium text-foreground text-sm md:text-base break-all'>
                      {formatAmount(bonus.amount, bonus.fund.currency)}
                    </p>
                    <p className='text-xs md:text-sm text-foreground-secondary flex items-center'>
                      <Calendar className='h-3 w-3 mr-1 flex-shrink-0' />
                      <span className='truncate'>
                        {formatDate(bonus.created_at)}
                      </span>
                    </p>
                  </div>

                  <div className='flex flex-row sm:flex-col items-start sm:items-end gap-2 sm:gap-1'>
                    <Badge className={getLevelBadgeColor(bonus.level)}>
                      Level {bonus.level}
                    </Badge>
                    {getStatusBadge(bonus)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
}
