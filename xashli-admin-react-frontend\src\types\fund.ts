// Fund management types matching <PERSON><PERSON> backend
import type { PaginationMeta, Currency } from './common';

export interface Fund {
  id: string;
  user_id: string;
  amount: number;
  currency: Currency;
  status: 'pending' | 'matched' | 'completed' | 'cancelled';
  platform_fee_percentage: number;
  growth_percentage: number;
  growth_amount: number;
  referral_bonus_limit: number;
  is_eligible_for_withdrawal: boolean;
  start_date: string;
  maturity_date: string;
  amount_matched: number;
  payment_method_id: string;
  prev_fund_id?: string;
  next_fund_id?: string;
  withdraw_id?: string;
  transaction_hash?: string;
  created_at: string;
  updated_at: string;
  // Relations (when included via API)
  user?: {
    id: string;
    full_name: string;
    email: string;
    role: 'user' | 'admin';
  };
  paymentMethod?: {
    id: string;
    type: 'bank' | 'crypto';
    label: string;
    bank_name?: string;
    account_number?: string;
    account_name?: string;
    wallet_address?: string;
    crypto_network?: string;
  };
  paymentMatches?: PaymentMatch[];
}

export interface PaymentMatch {
  id: string;
  fund_id: string;
  withdraw_id: string;
  matched_amount: number;
  amount: number; // alias for matched_amount for compatibility
  status: 'pending' | 'paid' | 'confirmed' | 'disputed' | 'cancelled';
  initiator: 'auto' | 'manual';
  transaction_hash?: string;
  payment_proof_image?: string;
  // New confirmation fields
  is_payment_sent_confirmed?: boolean;
  payment_sent_confirmed_at?: string;
  is_payment_received_confirmed?: boolean;
  payment_received_confirmed_at?: string;
  created_at: string;
  updated_at: string;
}

// Fund filter types
export interface FundFilters {
  status?: 'pending' | 'matched' | 'completed' | 'cancelled';
  currency?: 'fiat' | 'crypto';
  user_id?: string;
  email?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  sort_field?: string;
  sort_direction?: 'asc' | 'desc';
}

// API request/response types
export interface FundListParams extends FundFilters {
  page?: number;
  per_page?: number;
  sort_field?: string;
  sort_direction?: 'asc' | 'desc';
}

export interface FundListResponse {
  funds: Fund[];
  pagination: PaginationMeta;
}

// Fund statistics for dashboard
export interface FundStats {
  overview: {
    total_count: number;
    count: {
      fiat: number;
      crypto: number;
    };
    amount: {
      fiat: number;
      crypto: number;
    };
  };
  statuses: {
    pending: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    matched: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    completed: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    cancelled: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
  };
}

// Form types for fund operations
export interface CancelFundRequest {
  reason?: string;
}

export interface ConfirmPaymentRequest {
  transaction_hash: string;
}
