import type { User } from './user';
import type { Fund, PaymentMatch } from './fund';
import type { PaginationMeta } from './common';

export interface PaymentMethod {
  id: string;
  user_id: string;
  type: 'bank' | 'crypto';
  name: string;
  details: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Withdraw {
  id: string;
  user_id: string;
  fund_id: string;
  base_withdrawable_amount: number;
  available_referral_bonus: number;
  withdrawable_referral_bonus: number;
  total_withdrawable_amount: number;
  amount_matched: number;
  status: 'pending' | 'matched' | 'completed';
  payment_method_id: string;
  created_at: string;
  updated_at: string;

  // Relationships
  user?: User;
  fund?: Fund & {
    nextFund?: Fund;
  };
  payment_method?: PaymentMethod;
  payment_matches?: PaymentMatch[];

  // Computed properties
  total_to_fully_match?: number;
  total_withdrawal_amount?: number;
}

export interface WithdrawFilters {
  status?: 'pending' | 'matched' | 'completed' | '';
  currency?: 'fiat' | 'crypto' | '';
  user_id?: string;
  email?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
  sort_field?: string;
  sort_direction?: 'asc' | 'desc';
}

export interface WithdrawStats {
  overview: {
    total_count: number;
    count: {
      fiat: number;
      crypto: number;
    };
    amount: {
      fiat: number;
      crypto: number;
    };
  };
  statuses: {
    pending: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    matched: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    completed: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
  };
}

// API Response Types
export interface WithdrawsResponse {
  withdraws: Withdraw[];
  pagination: PaginationMeta;
}

export interface WithdrawStatsResponse {
  data: WithdrawStats;
  message: string;
}

export interface WithdrawResponse {
  data: Withdraw;
  message: string;
}

// API Request Types
export interface GetWithdrawsRequest extends WithdrawFilters {
  page?: number;
  per_page?: number;
}

export interface DisputePaymentRequest {
  match_id: string;
  reason: string;
}

// Form Types
export interface WithdrawSearchForm {
  search: string;
  status: string;
  currency: string;
  user_id: string;
  start_date: string;
  end_date: string;
}
