// User management types matching <PERSON><PERSON> backend
import type { PaginationMeta, Currency } from './common';

export interface User {
  id: string;
  full_name: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  profile_image?: string;
  role: 'user' | 'admin';
  has_premium_privilege: boolean;
  has_elite_privilege: boolean;
  elite_privilege_level: number;
  is_active: boolean;
  referral_code: string;
  referrer_code?: string;
  referrer_id?: string;
  referee_count: number;
  email_verified_at?: string;
  last_login?: string;
  created_at: string;
  updated_at: string;
  // Relations
  stats?: UserStats;
  paymentMethods?: PaymentMethod[];
  funds?: Fund[];
  withdraws?: Withdraw[];
  referees?: User[];
}

export interface UserStats {
  user_id: string;
  total_fiat_funded: number;
  total_crypto_funded: number;
  total_fiat_withdrawn: number;
  total_crypto_withdrawn: number;
  total_fiat_referral_bonus_earned: number;
  total_crypto_referral_bonus_earned: number;
  available_fiat_referral_bonus: number;
  available_crypto_referral_bonus: number;
  consumed_fiat_referral_bonus: number;
  consumed_crypto_referral_bonus: number;
  referee_count_level1: number;
  referee_count_level2: number;
  referee_count_level3: number;
  pending_fiat_withdrawal: number;
  pending_crypto_withdrawal: number;
  next_withdraw_eligibility?: string;
  updated_at?: string;
}

export interface PaymentMethod {
  id: string;
  user_id: string;
  type: 'bank' | 'crypto';
  bank_name?: string;
  account_number?: string;
  account_name?: string;
  wallet_address?: string;
  crypto_network?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Fund {
  id: string;
  user_id: string;
  amount: number;
  currency: Currency;
  status: 'pending' | 'matched' | 'completed' | 'cancelled';
  platform_fee_percentage: number;
  growth_percentage: number;
  growth_amount: number;
  referral_bonus_limit: number;
  is_eligible_for_withdrawal: boolean;
  start_date: string;
  maturity_date: string;
  amount_matched: number;
  payment_method_id: string;
  prev_fund_id?: string;
  next_fund_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Withdraw {
  id: string;
  user_id: string;
  fund_id: string;
  amount: number;
  status: 'pending' | 'matched' | 'completed';
  payment_method_id: string;
  created_at: string;
  updated_at: string;
  fund?: Fund;
  paymentMethod?: PaymentMethod;
}

// User management specific types
export interface UpdateUserRequest {
  first_name?: string;
  last_name?: string;
  full_name?: string;
  email?: string;
  password?: string;
  phone?: string;
  role?: 'user' | 'admin';
  is_active?: boolean;
}

export interface UserFilters {
  role?: 'user' | 'admin';
  is_active?: boolean;
  search?: string;
  sort_field?: string;
  sort_direction?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface PaginatedUsers {
  users: User[];
  pagination: PaginationMeta;
}

// Referral management types
export interface ReferralUser {
  id: string;
  full_name: string;
  email: string;
  referral_code: string;
  referee_count: number;
  level: number;
  total_funding: number;
  is_active: boolean;
  joined_at: string;
}

export interface ReferralBonus {
  id: string;
  fund_id: string;
  referrer_user_id: string;
  referee_user_id: string;
  level: number;
  percentage: number;
  amount: number;
  status: 'pending' | 'paid';
  created_at: string;
  updated_at: string;
  fund?: Fund;
  referrer?: User;
  referee?: User;
}

// Form types
export interface UserFormData {
  full_name: string;
  email: string;
  password?: string;
  phone?: string;
  role: 'user' | 'admin';
  is_active: boolean;
}
