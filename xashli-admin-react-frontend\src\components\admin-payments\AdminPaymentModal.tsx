import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading';
import {
  X,
  Copy,
  Check,
  User,
  CreditCard,
  Phone,
  Wallet,
  DollarSign,
  AlertCircle,
} from 'lucide-react';
import type { PaymentMatchDetails } from '@/types/paymentMatch';
import { formatCurrencyAmount } from '@/utils/format';
import { useCopyToClipboard } from '@/utils/copy';
import { toast } from 'react-hot-toast';

interface AdminPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (transactionHash: string) => Promise<void>;
  paymentMatch: PaymentMatchDetails;
  loading?: boolean;
}

export function AdminPaymentModal({
  isOpen,
  onClose,
  onConfirm,
  paymentMatch,
  loading = false,
}: AdminPaymentModalProps) {
  const [confirming, setConfirming] = useState(false);
  const [transactionHash, setTransactionHash] = useState('');
  const { copied, copy } = useCopyToClipboard();
  const amount = parseFloat(paymentMatch.amount || '0');
  const paymentMethod = paymentMatch.payment_method;
  const withdrawUser = paymentMatch.withdraw_user;
  const currency = paymentMatch.fund?.currency || 'fiat';

  const handleConfirm = async () => {
    setConfirming(true);

    try {
      // For crypto payments, transaction hash is required
      if (paymentMethod?.type === 'crypto') {
        if (!transactionHash.trim()) {
          toast.error('Transaction hash is required for crypto payments');
          return;
        }
      }

      // Use transaction hash or placeholder for fiat
      const hash =
        paymentMethod?.type === 'crypto'
          ? transactionHash.trim()
          : 'admin_fiat_payment';

      await onConfirm(hash);
      onClose();
    } catch (error) {
      console.error('Payment confirmation failed:', error);
      toast.error('Payment confirmation failed. Please try again.');
    } finally {
      setConfirming(false);
    }
  };

  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-white rounded-lg max-w-md w-full my-8 p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-brand-black">
              Admin Payment - Send Funds
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              disabled={confirming}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Amount Section */}
          <div className="bg-brand-gold-50 rounded-lg p-3 mb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-brand-gold-600" />
                <span className="text-sm font-medium text-brand-grey-900">
                  Payment Amount
                </span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-brand-gold-600">
                  {formatCurrencyAmount(amount, currency as 'fiat' | 'crypto')}
                </div>
                <div className="text-xs text-brand-grey-600">
                  ID: {paymentMatch.id.slice(0, 8)}...
                </div>
              </div>
            </div>
          </div>

          {/* Withdraw User Contact Information */}
          {withdrawUser && (
            <div className="bg-brand-grey-50 rounded-lg p-3 mb-3">
              <div className="flex items-center gap-2 mb-3">
                <User className="h-4 w-4 text-brand-gold-600" />
                <span className="text-sm font-medium text-brand-grey-900">
                  Recipient Information
                </span>
              </div>

              <div className="space-y-2">
                {/* Display withdraw user's name (person to be funded) */}
                <div>
                  <label className="text-xs text-brand-grey-600">
                    Full Name
                  </label>
                  <div className="text-sm font-medium text-brand-grey-900">
                    {withdrawUser.full_name}
                  </div>
                </div>

                {/* Display withdraw user's phone number (person to be funded) */}
                {withdrawUser.phone && (
                  <div>
                    <label className="text-xs text-brand-grey-600">
                      Phone Number
                    </label>
                    <div className="flex items-center gap-2 p-2 bg-white rounded border">
                      <Phone className="h-3 w-3 text-brand-grey-500 flex-shrink-0" />
                      <span className="text-sm font-medium text-brand-grey-900 flex-1">
                        {withdrawUser.phone}
                      </span>
                      <button
                        onClick={() => {
                          if (withdrawUser.phone) {
                            copy(
                              withdrawUser.phone,
                              'Phone number copied!',
                              'Failed to copy phone number'
                            );
                          }
                        }}
                        className="flex items-center justify-center w-6 h-6 rounded hover:bg-brand-grey-100 transition-colors"
                        title="Copy phone number"
                      >
                        {copied ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3 text-brand-grey-500 hover:text-brand-grey-700" />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Payment Method Details */}
          <div className="bg-brand-grey-50 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 mb-3">
              {paymentMethod?.type === 'bank' ? (
                <CreditCard className="h-4 w-4 text-brand-gold-600" />
              ) : (
                <Wallet className="h-4 w-4 text-brand-gold-600" />
              )}
              <span className="text-sm font-medium text-brand-grey-900">
                Payment Details
              </span>
            </div>

            {paymentMethod ? (
              <div className="space-y-2">
                {paymentMethod.type === 'bank' ? (
                  /* Bank Account Details */
                  <>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="text-xs text-brand-grey-600">
                          Bank Name
                        </label>
                        <div className="text-sm font-medium text-brand-grey-900">
                          {paymentMethod.bank_name || 'Not provided'}
                        </div>
                      </div>
                      <div>
                        <label className="text-xs text-brand-grey-600">
                          Account Name
                        </label>
                        <div className="text-sm font-medium text-brand-grey-900">
                          {paymentMethod.account_name || 'Not provided'}
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="text-xs text-brand-grey-600">
                        Account Number
                      </label>
                      <div className="flex items-center gap-2 p-2 bg-white rounded border">
                        <span className="text-sm font-mono font-medium text-brand-grey-900 flex-1">
                          {paymentMethod.account_number || 'Not provided'}
                        </span>
                        {paymentMethod.account_number && (
                          <button
                            onClick={() =>
                              copy(
                                paymentMethod.account_number!,
                                'Account number copied!',
                                'Failed to copy account number'
                              )
                            }
                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-brand-grey-100 transition-colors"
                            title="Copy account number"
                          >
                            {copied ? (
                              <Check className="h-3 w-3 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3 text-brand-grey-500 hover:text-brand-grey-700" />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  /* Crypto Wallet Details */
                  <>
                    <div>
                      <label className="text-xs text-brand-grey-600">
                        Network
                      </label>
                      <div className="text-sm font-medium text-brand-grey-900">
                        {paymentMethod.crypto_network || 'Not provided'}
                      </div>
                    </div>
                    <div>
                      <label className="text-xs text-brand-grey-600">
                        Wallet Address
                      </label>
                      <div className="flex items-center gap-2 p-2 bg-white rounded border">
                        <span className="text-xs font-mono text-brand-grey-900 break-all flex-1">
                          {paymentMethod.wallet_address || 'Not provided'}
                        </span>
                        {paymentMethod.wallet_address && (
                          <button
                            onClick={() =>
                              copy(
                                paymentMethod.wallet_address!,
                                'Wallet address copied!',
                                'Failed to copy wallet address'
                              )
                            }
                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-brand-grey-100 transition-colors"
                            title="Copy wallet address"
                          >
                            {copied ? (
                              <Check className="h-3 w-3 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3 text-brand-grey-500 hover:text-brand-grey-700" />
                            )}
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Transaction Hash Input for Crypto */}
                    <div className="mt-4">
                      <label className="text-xs text-brand-grey-600 block mb-1">
                        Transaction Hash *
                      </label>
                      <Input
                        type="text"
                        value={transactionHash}
                        onChange={e => setTransactionHash(e.target.value)}
                        placeholder="Enter transaction hash/signature"
                        disabled={confirming}
                        className="text-sm"
                      />
                      <p className="text-xs text-brand-gold-600 font-medium mt-1">
                        Enter the transaction hash after sending the payment
                      </p>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-2 p-2 bg-brand-gold-50 rounded border border-brand-gold-200">
                <AlertCircle className="h-4 w-4 text-brand-gold-600 flex-shrink-0" />
                <span className="text-sm text-brand-grey-800">
                  Payment method details not available
                </span>
              </div>
            )}
          </div>

          {/* Important Notice */}
          <div className="mb-4 p-2 bg-brand-grey-50 rounded border border-brand-grey-200">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-brand-gold-600 flex-shrink-0" />
              <p className="text-sm text-brand-grey-800">
                {paymentMethod?.type === 'crypto'
                  ? 'After making your payment, enter the transaction hash and click "I\'ve Sent Payment".'
                  : 'After making your payment, click "I\'ve Sent Payment" to confirm.'}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={onClose}
              disabled={loading || confirming}
            >
              Cancel
            </Button>
            <Button
              className="flex-1 bg-brand-gold-600 hover:bg-brand-gold-700 text-white"
              onClick={handleConfirm}
              disabled={
                loading ||
                confirming ||
                !paymentMethod ||
                (paymentMethod?.type === 'crypto' && !transactionHash.trim())
              }
            >
              {confirming ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Confirming...
                </>
              ) : (
                "I've Sent Payment"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
