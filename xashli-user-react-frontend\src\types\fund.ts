import type { PaymentMethod } from './paymentMethod';
import type { PaymentMatch } from './paymentMatch';
import type { Withdraw } from './withdraw';
import type { PaginationMeta, Currency } from './common';

export interface Fund {
  id: string;
  user_id: string;
  amount: number;
  currency: Currency;
  currency_code: string;
  payment_method_id: string;
  status: 'pending' | 'matched' | 'completed' | 'cancelled';
  start_date: string;
  maturity_date: string;
  maturity_days: number;
  growth_percentage: number;
  growth_amount: number;
  platform_fee_percentage: number;
  platform_fee_amount: number;
  referral_bonus_limit: number;
  next_fund_id?: string;
  prev_fund_id?: string;
  withdraw_id?: string;
  transaction_hash?: string;
  payment_proof_image?: string;
  created_at: string;
  updated_at: string;
  payment_method?: PaymentMethod;
  payment_matches?: PaymentMatch[];
  next_fund?: Fund;
  withdraws?: Withdraw[];
  user_withdraw?: Withdraw;
}

export interface CreateFundData {
  amount: number;
  currency: Currency;
  payment_method_id: string;
}

export interface FundStatistics {
  overview: {
    total_count: number;
    count: {
      fiat: number;
      crypto: number;
    };
    amount: {
      fiat: number;
      crypto: number;
    };
  };
  statuses: {
    pending: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    matched: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    completed: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    cancelled: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
  };
}

export interface FundFilters {
  status?: 'pending' | 'matched' | 'completed' | 'cancelled';
  currency?: 'fiat' | 'crypto';
  sort_field?: 'amount' | 'created_at' | 'status' | 'currency';
  sort_direction?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

export interface PaginatedFunds {
  funds: Fund[];
  pagination: PaginationMeta;
}

export interface ConfirmPaymentSentData {
  transaction_hash: string;
}

export interface UploadPaymentProofData {
  payment_proof: File;
}
