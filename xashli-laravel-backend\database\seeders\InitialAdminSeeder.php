<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\UserStat;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class InitialAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'id' => Str::uuid(),
            'full_name' => 'System Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin12345'),
            'role' => 'admin',
            'is_active' => true,
            'referral_code' => strtoupper(Str::random(8)),
            'referee_count' => 0,
            'email_verified_at' => now(),
        ]);

        // Create admin stats for tracking payment matching activities
        UserStat::create([
            'user_id' => $admin->id,
        ]);

        // Create bank payment method for admin (for fiat platform fees)
        PaymentMethod::create([
            'id' => Str::uuid(),
            'user_id' => $admin->id,
            'type' => 'bank',
            'bank_name' => 'Kuda MFB',
            'account_number' => '**********',
            'account_name' => 'Izuchukwu Amadi',
            'status' => 'active',
        ]);

        // Create crypto payment method for admin (for crypto platform fees)
        PaymentMethod::create([
            'id' => Str::uuid(),
            'user_id' => $admin->id,
            'type' => 'crypto',
            'wallet_address' => '2KFCH6QpLNsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvA',
            'crypto_network' => 'Solana',
            'status' => 'active',
        ]);
    }
}
