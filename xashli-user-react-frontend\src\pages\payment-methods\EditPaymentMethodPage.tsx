import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, CreditCard, Wallet, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ConfirmationCodeSection } from '../../components/ui/ConfirmationCodeSection';
import {
  useEditPaymentMethod,
  useBanks,
  useAccountValidation,
} from '../../hooks/payment-methods';
import { useConfirmationCode } from '../../hooks/useConfirmationCode';

export function EditPaymentMethodPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    paymentMethod,
    loading,
    saving,
    formData,
    errors,
    handleInputChange,
    updateFormData,
    clearError,
    handleSubmit,
    setAccountValidated,
  } = useEditPaymentMethod(id);

  const {
    banks,
    bankSearchTerm,
    setBankSearchTerm,
    loadingBanks,
    filteredBanks,
    loadBanks,
    getBankByCode,
    clearBankSearch,
  } = useBanks();

  const {
    validatingAccount,
    validateAccount,
    resetValidation,
  } = useAccountValidation();

  // Confirmation code hook
  const confirmationCode = useConfirmationCode({
    action: 'update',
    context: 'payment_method',
  });

  // Track the last validated account number to prevent unnecessary API calls
  const [lastValidatedAccountNumber, setLastValidatedAccountNumber] =
    useState<string>('');

  // Auto-validate account when 10 digits are entered
  const handleAutoValidation = useCallback(
    async (accountNumber: string, bankCode: string) => {
      if (
        accountNumber.length === 10 &&
        bankCode &&
        accountNumber !== lastValidatedAccountNumber
      ) {
        setLastValidatedAccountNumber(accountNumber);
        await validateAccount(
          bankCode,
          accountNumber,
          accountName => {
            updateFormData({ account_name: accountName });
            setAccountValidated(true);
            clearError('account_number');
            clearError('account_name');
          },
          () => {
            // Error handling is already done in the hook
          }
        );
      }
    },
    [
      lastValidatedAccountNumber,
      validateAccount,
      updateFormData,
      setAccountValidated,
      clearError,
    ]
  );

  useEffect(() => {
    if (paymentMethod?.type === 'bank') {
      loadBanks();
    }
  }, [paymentMethod]);

  const handleFormInputChange = (field: string, value: string) => {
    handleInputChange(field, value);

    // Set bank name when bank code is selected
    if (field === 'bank_code') {
      const selectedBank = getBankByCode(value);
      if (selectedBank) {
        updateFormData({ bank_name: selectedBank.name });
      }
    }

    // Clear validation when changing bank or account number
    if (field === 'bank_code' || field === 'account_number') {
      resetValidation();
      setAccountValidated(false);
      updateFormData({ account_name: '' });
      setLastValidatedAccountNumber('');
    }

    // Auto-validate account when 10 digits are entered
    if (
      field === 'account_number' &&
      value.length === 10 &&
      formData.bank_code
    ) {
      handleAutoValidation(value, formData.bank_code as string);
    }

    // Clear bank search when a bank is selected
    if (field === 'bank_code') {
      clearBankSearch();
    }
  };

  const onSubmit = (e: React.FormEvent) => {
    // Validate confirmation code
    if (!confirmationCode.isCodeValid) {
      return;
    }

    // Pass confirmation code directly to handleSubmit
    handleSubmit(
      e,
      () => navigate('/payment-methods'),
      confirmationCode.code
    );
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (!paymentMethod) {
    return null;
  }

  const isBankAccount = paymentMethod.type === 'bank';

  return (
    <div className='container mx-auto px-6 py-8 max-w-2xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => navigate('/payment-methods')}
          className='p-2'
        >
          <ArrowLeft className='h-4 w-4' />
        </Button>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Edit Payment Method
          </h1>
          <p className='text-foreground-secondary mt-2'>
            Update your {isBankAccount ? 'bank account' : 'crypto wallet'}{' '}
            details
          </p>
        </div>
      </div>

      <form onSubmit={onSubmit} className='space-y-6'>
        {/* Payment Method Type Display */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Method Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center gap-3 p-4 rounded-lg border-2 border-border bg-background-secondary'>
              {isBankAccount ? (
                <CreditCard className='h-8 w-8 text-primary' />
              ) : (
                <Wallet className='h-8 w-8 text-primary' />
              )}
              <div>
                <span className='font-medium'>
                  {isBankAccount ? 'Bank Account' : 'Crypto Wallet'}
                </span>
                <p className='text-sm text-foreground-secondary'>
                  {isBankAccount
                    ? 'Bank account for fiat transactions'
                    : 'Crypto wallet for digital asset transactions'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Method Details */}
        <Card>
          <CardHeader>
            <CardTitle>
              {isBankAccount ? 'Bank Account Details' : 'Crypto Wallet Details'}
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            {isBankAccount ? (
              <>
                {/* Bank Selection */}
                <div className='space-y-2'>
                  <Label>Bank *</Label>
                  <Select
                    value={formData.bank_code || ''}
                    onValueChange={(value: string) =>
                      handleFormInputChange('bank_code', value)
                    }
                  >
                    <SelectTrigger
                      className={errors.bank_code ? 'border-destructive' : ''}
                    >
                      {formData.bank_code ? (
                        <span>
                          {banks.find(bank => bank.code === formData.bank_code)
                            ?.name || 'Select your bank'}
                        </span>
                      ) : (
                        <SelectValue placeholder='Select your bank' />
                      )}
                    </SelectTrigger>
                    <SelectContent>
                      {loadingBanks ? (
                        <div className='flex items-center justify-center p-4'>
                          <LoadingSpinner size='sm' />
                        </div>
                      ) : (
                        <>
                          {/* Search Input */}
                          <div className='p-2 border-b border-border'>
                            <Input
                              placeholder='Search banks...'
                              value={bankSearchTerm}
                              onChange={e => setBankSearchTerm(e.target.value)}
                              onClick={e => e.stopPropagation()}
                              onKeyDown={e => e.stopPropagation()}
                              className='h-8 text-sm'
                            />
                          </div>
                          {/* Bank Options */}
                          <div className='max-h-48 overflow-auto'>
                            {filteredBanks.length === 0 ? (
                              <div className='p-2 text-sm text-foreground-secondary text-center'>
                                No banks found
                              </div>
                            ) : (
                              filteredBanks.map(bank => (
                                <SelectItem key={bank.code} value={bank.code}>
                                  {bank.name}
                                </SelectItem>
                              ))
                            )}
                          </div>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.bank_code && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.bank_code}
                    </p>
                  )}
                </div>

                {/* Account Number */}
                <div className='space-y-2'>
                  <Label htmlFor='account_number'>Account Number *</Label>
                  <div className='relative'>
                    <Input
                      id='account_number'
                      placeholder='Enter 10-digit account number'
                      value={formData.account_number || ''}
                      onChange={e =>
                        handleFormInputChange('account_number', e.target.value)
                      }
                      className={
                        errors.account_number ? 'border-destructive' : ''
                      }
                    />
                    {validatingAccount && (
                      <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
                        <LoadingSpinner size='sm' />
                      </div>
                    )}
                  </div>
                  {errors.account_number && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.account_number}
                    </p>
                  )}
                </div>

                {/* Account Name */}
                <div className='space-y-2'>
                  <Label htmlFor='account_name'>Account Name *</Label>
                  <Input
                    id='account_name'
                    placeholder='Account name will be auto-filled'
                    value={formData.account_name || ''}
                    onChange={e =>
                      handleFormInputChange('account_name', e.target.value)
                    }
                    className={errors.account_name ? 'border-destructive' : ''}
                    readOnly
                  />
                  {errors.account_name && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.account_name}
                    </p>
                  )}
                </div>
              </>
            ) : (
              <>
                {/* Crypto Network */}
                <div className='space-y-2'>
                  <Label>Crypto Network *</Label>
                  <Select
                    value={formData.crypto_network || ''}
                    onValueChange={(value: string) =>
                      handleFormInputChange('crypto_network', value)
                    }
                  >
                    <SelectTrigger
                      className={
                        errors.crypto_network ? 'border-destructive' : ''
                      }
                    >
                      <SelectValue placeholder='Select crypto network' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Solana'>Solana (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.crypto_network && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.crypto_network}
                    </p>
                  )}
                </div>

                {/* Wallet Address */}
                <div className='space-y-2'>
                  <Label htmlFor='wallet_address'>Wallet Address *</Label>
                  <Input
                    id='wallet_address'
                    placeholder='Enter your wallet address'
                    value={formData.wallet_address || ''}
                    onChange={e =>
                      handleFormInputChange('wallet_address', e.target.value)
                    }
                    className={`font-mono ${errors.wallet_address ? 'border-destructive' : ''}`}
                  />
                  {errors.wallet_address && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.wallet_address}
                    </p>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Confirmation Code Section */}
        <ConfirmationCodeSection
          code={confirmationCode.code}
          onCodeChange={confirmationCode.setCode}
          onRequestCode={confirmationCode.requestCode}
          isRequesting={confirmationCode.isRequesting}
          isRequested={confirmationCode.isRequested}
          canRequest={confirmationCode.canRequest}
          cooldownRemaining={confirmationCode.cooldownRemaining}
          error={confirmationCode.error}
          title='Security Confirmation Required'
          description='For your security, please request and enter a confirmation code to update this payment method.'
        />

        {/* Form Actions */}
        <div className='flex items-center justify-end gap-4 pt-6 border-t border-border'>
          <Button
            type='button'
            variant='outline'
            onClick={() => navigate('/payment-methods')}
            disabled={saving}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            disabled={saving || !confirmationCode.isCodeValid}
          >
            {saving ? (
              <>
                <LoadingSpinner size='sm' className='mr-2' />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
