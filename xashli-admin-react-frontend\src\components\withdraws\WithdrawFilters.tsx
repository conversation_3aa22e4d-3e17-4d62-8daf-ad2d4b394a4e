import { useState, useEffect } from 'react';
import { Search, Filter, X } from 'lucide-react';

import type { WithdrawFilters } from '../../types/withdraw';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Badge } from '../ui/badge';

interface WithdrawFiltersProps {
  filters: WithdrawFilters;
  onFiltersChange: (filters: WithdrawFilters) => void;
}

export function WithdrawFilters({
  filters,
  onFiltersChange,
}: WithdrawFiltersProps) {
  const [localFilters, setLocalFilters] = useState<WithdrawFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);
  const handleFilterChange = (
    key: keyof WithdrawFilters,
    value: string | undefined
  ) => {
    const newFilters = { ...localFilters };
    if (value && value !== '') {
      (newFilters as any)[key] = value;
    } else {
      delete newFilters[key];
    }
    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
  };

  const clearFilters = () => {
    const emptyFilters: WithdrawFilters = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(localFilters).filter(Boolean).length;
  };

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {/* Search */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Search</label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search amounts..."
              value={localFilters.search || ''}
              onChange={e => handleFilterChange('search', e.target.value)}
              className="pl-9"
            />
          </div>
        </div>{' '}
        {/* Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Status</label>
            {localFilters.status && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('status', undefined)}
                className="h-auto p-1 text-xs"
              >
                Clear
              </Button>
            )}
          </div>
          <Select
            value={localFilters.status || undefined}
            onValueChange={value => handleFilterChange('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="matched">Matched</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>{' '}
        {/* Currency */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Currency</label>
            {localFilters.currency && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('currency', undefined)}
                className="h-auto p-1 text-xs"
              >
                Clear
              </Button>
            )}
          </div>
          <Select
            value={localFilters.currency || undefined}
            onValueChange={value => handleFilterChange('currency', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All currencies" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fiat">Fiat</SelectItem>
              <SelectItem value="crypto">Crypto</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* User ID */}
        <div className="space-y-2">
          <label className="text-sm font-medium">User ID</label>
          <Input
            placeholder="Enter user ID..."
            value={localFilters.user_id || ''}
            onChange={e => handleFilterChange('user_id', e.target.value)}
          />
        </div>
        {/* Start Date */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Start Date</label>
          <Input
            type="date"
            value={localFilters.start_date || ''}
            onChange={e => handleFilterChange('start_date', e.target.value)}
          />
        </div>
        {/* End Date */}
        <div className="space-y-2">
          <label className="text-sm font-medium">End Date</label>
          <Input
            type="date"
            value={localFilters.end_date || ''}
            onChange={e => handleFilterChange('end_date', e.target.value)}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button onClick={applyFilters} size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Apply Filters
          </Button>
          {getActiveFiltersCount() > 0 && (
            <Button onClick={clearFilters} variant="outline" size="sm">
              <X className="mr-2 h-4 w-4" />
              Clear ({getActiveFiltersCount()})
            </Button>
          )}
        </div>

        {/* Active Filters Display */}
        {getActiveFiltersCount() > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Active filters:
            </span>
            <div className="flex gap-1">
              {localFilters.status && (
                <Badge variant="secondary" className="text-xs">
                  Status: {localFilters.status}
                </Badge>
              )}
              {localFilters.currency && (
                <Badge variant="secondary" className="text-xs">
                  Currency: {localFilters.currency}
                </Badge>
              )}
              {localFilters.user_id && (
                <Badge variant="secondary" className="text-xs">
                  User: {localFilters.user_id.slice(0, 8)}...
                </Badge>
              )}
              {localFilters.search && (
                <Badge variant="secondary" className="text-xs">
                  Search: {localFilters.search}
                </Badge>
              )}
              {(localFilters.start_date || localFilters.end_date) && (
                <Badge variant="secondary" className="text-xs">
                  Date Range
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
