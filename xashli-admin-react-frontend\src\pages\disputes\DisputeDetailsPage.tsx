import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  AlertTriangle,
  User,
  CreditCard,
  MessageSquare,
  FileText,
  Loader2,
} from 'lucide-react';
import { MainLayout } from '../../components/layout';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import {
  formatDateTime,
  formatCurrencyAmount,
  shortenId,
} from '../../utils/format';
import { useDispute } from '../../hooks/useDisputes';
import { DisputeResolveModal } from './DisputeResolveModal';

// Get status badge variant
const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'under_review':
      return {
        variant: 'secondary' as const,
        className: 'bg-yellow-100 text-yellow-800',
      };
    case 'resolved':
      return {
        variant: 'default' as const,
        className: 'bg-green-100 text-green-800',
      };
    case 'rejected':
      return {
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800',
      };
    default:
      return { variant: 'secondary' as const, className: '' };
  }
};

// Get resolution badge variant
const getResolutionBadgeVariant = (resolution: string) => {
  switch (resolution) {
    case 'confirmed':
      return {
        variant: 'default' as const,
        className: 'bg-green-100 text-green-800',
      };
    case 'cancelled':
      return {
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800',
      };
    case 'partial_refund':
      return {
        variant: 'secondary' as const,
        className: 'bg-blue-100 text-blue-800',
      };
    default:
      return { variant: 'secondary' as const, className: '' };
  }
};

export const DisputeDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [showResolveModal, setShowResolveModal] = useState(false);

  // Fetch dispute data using the hook
  const { data: disputeResponse, isLoading, error } = useDispute(id || '');
  const dispute = disputeResponse?.data;

  const handleResolve = () => {
    setShowResolveModal(true);
  };

  const handleCloseResolveModal = () => {
    setShowResolveModal(false);
  };

  console.log({ dispute });

  // Loading state
  if (isLoading) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/disputes')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Disputes</span>
            </Button>
          </div>
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">
              Loading dispute details...
            </span>
          </div>
        </div>
      </MainLayout>
    );
  }
  // Error state
  if (error || !dispute) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/disputes')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Disputes</span>
            </Button>
          </div>
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Dispute not found
            </h3>
            <p className="text-gray-500">
              The dispute you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const statusBadge = getStatusBadgeVariant(dispute.status);
  const resolutionBadge = dispute.resolution
    ? getResolutionBadgeVariant(dispute.resolution)
    : null;
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/disputes')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Disputes</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold flex items-center space-x-2">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
                <span>Dispute Details - #{dispute.id}</span>
              </h1>
              <p className="text-gray-600 mt-1">
                View detailed information about this payment dispute
              </p>
            </div>
          </div>
          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            {dispute.status === 'under_review' && (
              <Button
                onClick={handleResolve}
                className="bg-brand-gold-500 hover:bg-brand-gold-600 text-black"
              >
                Resolve Dispute
              </Button>
            )}
          </div>
        </div>
        {/* Status and Basic Info */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Dispute Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Status:</span>
                  <Badge
                    variant={statusBadge.variant}
                    className={statusBadge.className}
                  >
                    {dispute.status.replace('_', ' ')}
                  </Badge>
                </div>
                {dispute.resolution && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Resolution:</span>
                    <Badge
                      variant={resolutionBadge!.variant}
                      className={resolutionBadge!.className}
                    >
                      {dispute.resolution.replace('_', ' ')}
                    </Badge>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Disputed At:</span>
                  <span className="text-sm font-medium">
                    {formatDateTime(dispute.disputed_at)}
                  </span>
                </div>
                {dispute.resolved_at && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Resolved At:</span>
                    <span className="text-sm font-medium">
                      {formatDateTime(dispute.resolved_at)}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Payment Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Payment Match ID:
                  </span>
                  <span
                    className="text-sm font-medium"
                    title={dispute.payment_match_id}
                  >
                    #{shortenId(dispute.payment_match_id)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Amount:</span>
                  <span className="text-sm font-medium">
                    {formatCurrencyAmount(
                      dispute.payment_match?.amount,
                      dispute.payment_match?.fund?.currency || 'fiat'
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Currency:</span>
                  <span className="text-sm font-medium">
                    {dispute.payment_match?.fund?.currency || 'N/A'}
                  </span>
                </div>
                {dispute.refund_amount && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      Refund Amount:
                    </span>
                    <span className="text-sm font-medium text-red-600">
                      {formatCurrencyAmount(
                        parseFloat(dispute.refund_amount),
                        dispute.payment_match?.fund?.currency || 'fiat'
                      )}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Information */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Disputing User</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {' '}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Name:</span>
                  <span className="text-sm font-medium">
                    {dispute.dispute_user?.full_name || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Email:</span>
                  <span className="text-sm font-medium">
                    {dispute.dispute_user?.email || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">User ID:</span>
                  <span className="text-sm font-medium">
                    #{dispute.dispute_user_id}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>{' '}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4" />
                <span>Related Users</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Fund User:</span>
                  <span className="text-sm font-medium">
                    {dispute.payment_match?.fund?.user?.full_name || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Withdraw User:</span>
                  <span className="text-sm font-medium">
                    {dispute.payment_match?.withdraw?.user?.full_name || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Fund ID:</span>
                  <span className="text-sm font-medium">
                    #{dispute.payment_match?.fund_id || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Withdraw ID:</span>
                  <span className="text-sm font-medium">
                    #{dispute.payment_match?.withdraw_id || 'N/A'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Dispute Reason */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4" />
              <span>Dispute Reason</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-900 whitespace-pre-wrap">
                {dispute.reason || 'No reason provided'}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Resolution Notes */}
        {dispute.resolution_notes && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>Resolution Notes</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-gray-900 whitespace-pre-wrap">
                  {dispute.resolution_notes}
                </p>
              </div>
              {dispute.resolve_user && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    Resolved by: {dispute.resolve_user.full_name} (
                    {dispute.resolve_user.email})
                  </p>
                </div>
              )}
            </CardContent>{' '}
          </Card>
        )}

        {/* Resolve Modal */}
        <DisputeResolveModal
          dispute={dispute}
          isOpen={showResolveModal}
          onClose={handleCloseResolveModal}
        />
      </div>
    </MainLayout>
  );
};

export default DisputeDetailsPage;
