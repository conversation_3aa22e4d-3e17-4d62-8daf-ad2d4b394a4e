import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link, useNavigate } from 'react-router-dom';
import {
  Users,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  RefreshCw,
  X,
} from 'lucide-react';
import { MainLayout } from '../../components/layout';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { LoadingSpinner } from '../../components/ui/loading';
import { Pagination } from '../../components/ui/pagination';
import { userService } from '../../services/user';
import { showToast } from '../../utils';
import type { User, UserFilters } from '../../types/user';

interface UsersTableProps {
  users: User[];
  isLoading: boolean;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onToggleStatus: (user: User) => void;
}

const UsersTable: React.FC<UsersTableProps> = ({
  users,
  isLoading,
  onEdit,
  onDelete,
  onToggleStatus,
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No users found
        </h3>
        <p className="text-gray-500">Get started by creating a new user.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Referrals
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Joined
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {users.map(user => (
            <tr key={user.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-brand-gold-500 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-black">
                      {user.full_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">
                      {user.full_name}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    user.role === 'admin'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {user.role
                    .replace('_', ' ')
                    .replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    user.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {user.referee_count}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(user.created_at).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end space-x-2">
                  <Link
                    to={`/users/${user.id}`}
                    className="text-brand-gold-600 hover:text-brand-gold-900"
                  >
                    <Eye className="h-4 w-4" />
                  </Link>
                  <button
                    onClick={() => onEdit(user)}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onToggleStatus(user)}
                    className={`${user.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                  >
                    {user.is_active ? (
                      <UserX className="h-4 w-4" />
                    ) : (
                      <UserCheck className="h-4 w-4" />
                    )}
                  </button>
                  <button
                    onClick={() => onDelete(user)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export const UsersListPage: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<UserFilters>({
    page: 1,
    per_page: 15,
    sort_field: 'created_at',
    sort_direction: 'desc',
  });
  const [pendingFilters, setPendingFilters] = useState<UserFilters>({
    page: 1,
    per_page: 15,
    sort_field: 'created_at',
    sort_direction: 'desc',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);

  // Sync pendingFilters with filters when filters change
  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['users', filters, currentPage, pageSize],
    queryFn: () =>
      userService.getUsers({
        ...filters,
        page: currentPage,
        per_page: pageSize,
      }),
    placeholderData: previousData => previousData,
  });

  const handleFilterChange = (
    key: keyof UserFilters,
    value: string | number | boolean | undefined
  ) => {
    setPendingFilters(prev => {
      const newFilters = { ...prev };
      if (value !== undefined && value !== '') {
        (newFilters as any)[key] = value;
      } else {
        delete newFilters[key];
      }
      return newFilters;
    });
  };

  const applyFilters = () => {
    const finalFilters = { ...pendingFilters };
    if (searchTerm) {
      finalFilters.search = searchTerm;
    }
    setFilters(finalFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    const clearedFilters = {
      page: 1,
      per_page: 15,
      sort_field: 'created_at',
      sort_direction: 'desc',
    } as UserFilters;
    setPendingFilters(clearedFilters);
    setFilters(clearedFilters);
    setSearchTerm('');
    setCurrentPage(1); // Reset to first page when clearing filters
  };

  const hasFilterChanges = () => {
    const currentFiltersWithoutSearch = { ...filters };
    delete currentFiltersWithoutSearch.search;

    return (
      JSON.stringify(currentFiltersWithoutSearch) !==
        JSON.stringify(pendingFilters) ||
      (searchTerm !== '' && !filters.search) ||
      (searchTerm === '' && filters.search)
    );
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const handleEdit = (user: User) => {
    navigate(`/users/${user.id}/edit`);
  };

  const handleDelete = async (user: User) => {
    if (
      !window.confirm(
        `Are you sure you want to delete ${user.full_name}? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const response = await userService.deleteUser(user.id);

      if (response.status === 'success') {
        showToast.success('User deleted successfully');
        refetch();
      } else {
        showToast.error(response.message || 'Failed to delete user');
      }
    } catch {
      showToast.error('Failed to delete user');
    }
  };

  const handleToggleStatus = async (user: User) => {
    try {
      const response = await userService.updateUser(user.id, {
        is_active: !user.is_active,
      });

      if (response.status === 'success') {
        showToast.success(
          `User ${user.is_active ? 'deactivated' : 'activated'} successfully`
        );
        refetch();
      } else {
        showToast.error(response.message || 'Failed to update user status');
      }
    } catch {
      showToast.error('Failed to update user status');
    }
  };

  const users = usersData?.data?.users || [];
  const pagination = usersData?.data?.pagination || null;

  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  if (error) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">Failed to load users</div>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              User Management
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage all users in the system
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Total Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination?.total || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Active Users
              </CardTitle>
            </CardHeader>{' '}
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {users.filter((u: User) => u.is_active).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Admins
              </CardTitle>
            </CardHeader>{' '}
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {users.filter((u: User) => u.role === 'admin').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Users
              </CardTitle>
            </CardHeader>{' '}
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {users.filter((u: User) => u.role === 'user').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Filters
              </CardTitle>
              <CardDescription>
                Filter users by various criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Search Users</label>
                    {searchTerm && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSearchTerm('')}
                        className="h-auto p-1 text-xs"
                      >
                        <X className="h-3 w-3 mr-1" />
                        Clear
                      </Button>
                    )}
                  </div>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search users..."
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* User Role Filter */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Role</label>
                    {pendingFilters.role && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFilterChange('role', undefined)}
                        className="h-auto p-1 text-xs"
                      >
                        <X className="h-3 w-3 mr-1" />
                        Clear
                      </Button>
                    )}
                  </div>
                  <select
                    value={pendingFilters.role || ''}
                    onChange={e =>
                      handleFilterChange('role', e.target.value || undefined)
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold-500"
                  >
                    <option value="">All Roles</option>
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Status</label>
                    {pendingFilters.is_active !== undefined && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleFilterChange('is_active', undefined)
                        }
                        className="h-auto p-1 text-xs"
                      >
                        <X className="h-3 w-3 mr-1" />
                        Clear
                      </Button>
                    )}
                  </div>
                  <select
                    value={
                      pendingFilters.is_active === undefined
                        ? ''
                        : pendingFilters.is_active.toString()
                    }
                    onChange={e =>
                      handleFilterChange(
                        'is_active',
                        e.target.value === ''
                          ? undefined
                          : e.target.value === 'true'
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold-500"
                  >
                    <option value="">All Status</option>
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                  </select>
                </div>

                {/* Refresh Button */}
                <div className="space-y-2">
                  <label className="text-sm font-medium opacity-0">
                    Actions
                  </label>
                  <Button
                    variant="outline"
                    onClick={() => refetch()}
                    className="w-full"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </div>

              {/* Filter Action Buttons */}
              <div className="flex justify-end gap-3 mt-6">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters {hasFilterChanges() && '(*)'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Users Table */}
        <Card>
          <CardHeader>
            <CardTitle>Users</CardTitle>
            <CardDescription>
              {pagination &&
                `Showing ${pagination.from} to ${pagination.to} of ${pagination.total} users`}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <UsersTable
              users={users}
              isLoading={isLoading}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onToggleStatus={handleToggleStatus}
            />
          </CardContent>

          {/* Pagination */}
          <Pagination
            pagination={pagination}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            isLoading={isLoading}
            itemLabel="Users"
          />
        </Card>
      </div>
    </MainLayout>
  );
};
