import {
  User,
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  Filter,
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { useState } from 'react';
import type { RefereesApiResponse } from '../../types/referral';

interface RefereesListProps {
  referees: RefereesApiResponse;
}

export function RefereesList({ referees }: RefereesListProps) {
  const { referees: refereesData, counts } = referees;

  // State for level filters
  const [visibleLevels, setVisibleLevels] = useState({
    level1: true,
    level2: true,
    level3: true,
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getLevelColor = (level: number) => {
    switch (level) {
      case 1:
        return 'bg-blue-500/10 text-blue-600 border-blue-200';
      case 2:
        return 'bg-green-500/10 text-green-600 border-green-200';
      case 3:
        return 'bg-purple-500/10 text-purple-600 border-purple-200';
      default:
        return 'bg-gray-500/10 text-gray-600 border-gray-200';
    }
  };

  const handleLevelToggle = (level: 'level1' | 'level2' | 'level3') => {
    setVisibleLevels(prev => ({
      ...prev,
      [level]: !prev[level],
    }));
  };

  const isAnyLevelVisible = Object.values(visibleLevels).some(Boolean);

  return (
    <div className='space-y-6'>
      {/* Summary Cards */}
      <div className='grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4'>
        <Card className='p-3 md:p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-xs md:text-sm font-medium text-foreground-secondary'>
                Level 1
              </p>
              <p className='text-lg md:text-xl font-bold text-primary'>
                {counts.level_1}
              </p>
            </div>
            <Badge className={`${getLevelColor(1)} text-xs`}>Direct</Badge>
          </div>
        </Card>
        <Card className='p-3 md:p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-xs md:text-sm font-medium text-foreground-secondary'>
                Level 2
              </p>
              <p className='text-lg md:text-xl font-bold text-primary'>
                {counts.level_2}
              </p>
            </div>
            <Badge className={`${getLevelColor(2)} text-xs`}>Indirect</Badge>
          </div>
        </Card>
        <Card className='p-3 md:p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-xs md:text-sm font-medium text-foreground-secondary'>
                Level 3
              </p>
              <p className='text-lg md:text-xl font-bold text-primary'>
                {counts.level_3}
              </p>
            </div>
            <Badge className={`${getLevelColor(3)} text-xs`}>Sub-level</Badge>
          </div>
        </Card>
        <Card className='p-3 md:p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-xs md:text-sm font-medium text-foreground-secondary'>
                Total
              </p>
              <p className='text-lg md:text-xl font-bold text-primary'>
                {counts.total}
              </p>
            </div>
            <Users className='h-4 md:h-5 w-4 md:w-5 text-foreground-muted' />
          </div>
        </Card>
      </div>

      {/* Level Filter Controls */}
      <Card className='p-4'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0'>
          <div className='flex items-center space-x-2'>
            <Filter className='h-4 w-4 text-foreground-muted' />
            <h3 className='text-sm font-medium text-foreground'>
              Filter by Level:
            </h3>
          </div>

          <div className='flex flex-wrap items-center gap-4'>
            <label className='flex items-center space-x-2 cursor-pointer'>
              <input
                type='checkbox'
                checked={visibleLevels.level1}
                onChange={() => handleLevelToggle('level1')}
                className='w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'
              />
              <span className='text-sm text-blue-600 font-medium'>
                Level 1 ({counts.level_1})
              </span>
            </label>

            <label className='flex items-center space-x-2 cursor-pointer'>
              <input
                type='checkbox'
                checked={visibleLevels.level2}
                onChange={() => handleLevelToggle('level2')}
                className='w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500'
              />
              <span className='text-sm text-green-600 font-medium'>
                Level 2 ({counts.level_2})
              </span>
            </label>

            <label className='flex items-center space-x-2 cursor-pointer'>
              <input
                type='checkbox'
                checked={visibleLevels.level3}
                onChange={() => handleLevelToggle('level3')}
                className='w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500'
              />
              <span className='text-sm text-purple-600 font-medium'>
                Level 3 ({counts.level_3})
              </span>
            </label>
          </div>
        </div>
      </Card>

      {/* Referees Lists */}
      {[1, 2, 3].map(level => {
        const levelKey = `level${level}` as 'level1' | 'level2' | 'level3';
        const levelData = refereesData[`level${level as 1 | 2 | 3}`];

        // Don't render if level is not visible or has no data
        if (!levelData.length || !visibleLevels[levelKey]) return null;

        return (
          <Card key={level} className='p-4 md:p-6'>
            <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6'>
              <div className='flex items-center space-x-3 mb-2 sm:mb-0'>
                <Badge className={`${getLevelColor(level)} text-xs`}>
                  Level {level}
                </Badge>
                <h3 className='text-base md:text-lg font-semibold text-foreground'>
                  {level === 1
                    ? 'Direct Referees'
                    : level === 2
                      ? 'Indirect Referees'
                      : 'Sub-level Referees'}
                </h3>
              </div>
              <div className='text-xs md:text-sm text-foreground-secondary'>
                {levelData.length}{' '}
                {levelData.length === 1 ? 'referee' : 'referees'}
              </div>
            </div>

            <div className='grid grid-cols-1 gap-3 md:gap-4'>
              {levelData.map((referee: any) => (
                <div
                  key={referee.id}
                  className='flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 bg-background-tertiary rounded-lg border border-border hover:border-primary/20 transition-colors'
                >
                  <div className='flex items-center space-x-3 mb-2 sm:mb-0'>
                    <div className='w-8 h-8 md:w-10 md:h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0'>
                      <User className='h-4 w-4 md:h-5 md:w-5 text-primary' />
                    </div>
                    <div className='min-w-0 flex-1'>
                      <p className='text-sm md:text-base font-medium text-foreground truncate'>
                        {referee.full_name}
                      </p>
                      <p className='text-xs md:text-sm text-foreground-secondary truncate'>
                        {referee.email}
                      </p>
                    </div>
                  </div>

                  <div className='flex flex-wrap items-center gap-2 sm:gap-3'>
                    <div className='flex items-center space-x-1 text-xs sm:text-sm text-foreground-secondary whitespace-nowrap'>
                      <Calendar className='h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0' />
                      <span>{formatDate(referee.created_at)}</span>
                    </div>
                    <div className='flex items-center space-x-1 text-xs sm:text-sm text-foreground-secondary whitespace-nowrap'>
                      <Users className='h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0' />
                      <span>{referee.referee_count}</span>
                    </div>
                    <div className='flex items-center space-x-1'>
                      {referee.is_active ? (
                        <CheckCircle className='h-3 w-3 sm:h-4 sm:w-4 text-success flex-shrink-0' />
                      ) : (
                        <XCircle className='h-3 w-3 sm:h-4 sm:w-4 text-destructive flex-shrink-0' />
                      )}
                      <span className='text-xs sm:text-sm text-foreground-secondary whitespace-nowrap'>
                        {referee.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        );
      })}

      {/* No visible referees message */}
      {!isAnyLevelVisible && (
        <Card className='p-8 text-center'>
          <Filter className='h-12 w-12 text-foreground-muted mx-auto mb-4' />
          <h3 className='text-lg font-medium text-foreground mb-2'>
            No Levels Selected
          </h3>
          <p className='text-foreground-secondary'>
            Please select at least one level to view referees.
          </p>
        </Card>
      )}

      {/* No referees message */}
      {isAnyLevelVisible && counts.total === 0 && (
        <Card className='p-8 text-center'>
          <Users className='h-12 w-12 text-foreground-muted mx-auto mb-4' />
          <h3 className='text-lg font-medium text-foreground mb-2'>
            No Referees Yet
          </h3>
          <p className='text-foreground-secondary'>
            Start sharing your referral code to build your network!
          </p>
        </Card>
      )}
    </div>
  );
}
