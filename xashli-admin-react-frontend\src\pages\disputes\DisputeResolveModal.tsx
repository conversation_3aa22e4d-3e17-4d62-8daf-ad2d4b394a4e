import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { LoadingSpinner } from '../../components/ui/loading';
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  DollarSign,
  Info,
} from 'lucide-react';
import { useResolveDispute, useRejectDispute } from '../../hooks/useDisputes';
import { formatCurrencyAmount, shortenId } from '../../utils/format';
import type {
  PaymentDispute,
  DisputeResolveFormData,
  DisputeRejectFormData,
} from '../../types/dispute';

interface DisputeResolveModalProps {
  dispute: PaymentDispute;
  isOpen: boolean;
  onClose: () => void;
}

type ActionType = 'resolve' | 'reject';

export const DisputeResolveModal: React.FC<DisputeResolveModalProps> = ({
  dispute,
  isOpen,
  onClose,
}) => {
  const [actionType, setActionType] = useState<ActionType>('resolve');

  // Mutations
  const resolveDispute = useResolveDispute();
  const rejectDispute = useRejectDispute();

  // Form for resolving dispute
  const resolveForm = useForm<DisputeResolveFormData>({
    defaultValues: {
      resolution: 'confirmed',
      refund_amount: '',
      resolution_notes: '',
    },
  });

  // Form for rejecting dispute
  const rejectForm = useForm<DisputeRejectFormData>({
    defaultValues: {
      resolution_notes: '',
    },
  });

  const watchedResolution = resolveForm.watch('resolution');
  const isPartialRefund = watchedResolution === 'partial_refund';

  const handleResolve = async (data: DisputeResolveFormData) => {
    try {
      // Validate refund amount for partial refund
      if (data.resolution === 'partial_refund') {
        const refundAmount = parseFloat(data.refund_amount || '0');
        const originalAmount = parseFloat(dispute.payment_match?.amount || '0');

        if (!data.refund_amount || refundAmount <= 0) {
          resolveForm.setError('refund_amount', {
            type: 'manual',
            message: 'Refund amount is required for partial refund',
          });
          return;
        }

        if (refundAmount > originalAmount) {
          resolveForm.setError('refund_amount', {
            type: 'manual',
            message: 'Refund amount cannot exceed original amount',
          });
          return;
        }
      }

      await resolveDispute.mutateAsync({
        id: dispute.id.toString(),
        data: {
          resolution: data.resolution,
          refund_amount:
            data.resolution === 'partial_refund'
              ? data.refund_amount
              : undefined,
          resolution_notes: data.resolution_notes || undefined,
        },
      });

      onClose();
    } catch (error) {
      console.error('Failed to resolve dispute:', error);
    }
  };

  const handleReject = async (data: DisputeRejectFormData) => {
    try {
      await rejectDispute.mutateAsync({
        id: dispute.id.toString(),
        data: {
          resolution_notes: data.resolution_notes || undefined,
        },
      });

      onClose();
    } catch (error) {
      console.error('Failed to reject dispute:', error);
    }
  };

  const isLoading = resolveDispute.isPending || rejectDispute.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <span>Resolve Dispute - #{dispute.id}</span>
          </DialogTitle>
          <DialogDescription>
            Choose how to resolve this payment dispute
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Dispute Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Dispute Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Match:</span>
                  <span
                    className="font-medium"
                    title={dispute.payment_match_id}
                  >
                    #{shortenId(dispute.payment_match_id)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">
                    {formatCurrencyAmount(
                      dispute.payment_match?.amount,
                      dispute.payment_match?.fund?.currency || 'fiat'
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Dispute User:</span>
                  <span className="font-medium">
                    {dispute.dispute_user?.full_name || 'Unknown'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Choose Action</Label>
            <div className="grid gap-3 md:grid-cols-2">
              <div
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  actionType === 'resolve'
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setActionType('resolve')}
              >
                <div className="flex items-center space-x-3">
                  <CheckCircle
                    className={`h-5 w-5 ${actionType === 'resolve' ? 'text-green-600' : 'text-gray-400'}`}
                  />
                  <div>
                    <h3 className="font-medium">Resolve Dispute</h3>
                    <p className="text-sm text-gray-600">
                      Accept and resolve the dispute
                    </p>
                  </div>
                </div>
              </div>

              <div
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  actionType === 'reject'
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setActionType('reject')}
              >
                <div className="flex items-center space-x-3">
                  <XCircle
                    className={`h-5 w-5 ${actionType === 'reject' ? 'text-red-600' : 'text-gray-400'}`}
                  />
                  <div>
                    <h3 className="font-medium">Reject Dispute</h3>
                    <p className="text-sm text-gray-600">
                      Reject the dispute claim
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Resolve Form */}
          {actionType === 'resolve' && (
            <form
              onSubmit={resolveForm.handleSubmit(handleResolve)}
              className="space-y-4"
            >
              <div>
                <Label htmlFor="resolution">Resolution Type</Label>
                <Select
                  value={resolveForm.watch('resolution')}
                  onValueChange={value =>
                    resolveForm.setValue('resolution', value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="confirmed">
                      Confirm Payment Match
                    </SelectItem>
                    <SelectItem value="cancelled">
                      Cancel Payment Match
                    </SelectItem>
                    <SelectItem value="partial_refund">
                      Partial Refund
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  {watchedResolution === 'confirmed' &&
                    'Confirm that the payment match is valid and correct'}
                  {watchedResolution === 'cancelled' &&
                    'Cancel the payment match and return funds'}
                  {watchedResolution === 'partial_refund' &&
                    'Process a partial refund to the dispute user'}
                </p>
              </div>

              {/* Refund Amount (only for partial refund) */}
              {isPartialRefund && (
                <div>
                  <Label htmlFor="refund_amount">Refund Amount</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="refund_amount"
                      type="number"
                      step="0.01"
                      min="0"
                      max={dispute.payment_match?.amount || '0'}
                      placeholder="0.00"
                      className="pl-10"
                      {...resolveForm.register('refund_amount', {
                        required: isPartialRefund
                          ? 'Refund amount is required'
                          : false,
                        min: {
                          value: 0.01,
                          message: 'Refund amount must be greater than 0',
                        },
                        max: {
                          value: parseFloat(
                            dispute.payment_match?.amount || '0'
                          ),
                          message:
                            'Refund amount cannot exceed original amount',
                        },
                      })}
                    />
                  </div>
                  {resolveForm.formState.errors.refund_amount && (
                    <p className="text-sm text-red-600 mt-1">
                      {resolveForm.formState.errors.refund_amount.message}
                    </p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum:{' '}
                    {formatCurrencyAmount(
                      dispute.payment_match?.amount,
                      dispute.payment_match?.fund?.currency || 'fiat'
                    )}
                  </p>
                </div>
              )}

              {/* Resolution Notes */}
              <div>
                <Label htmlFor="resolution_notes">
                  Resolution Notes (Optional)
                </Label>
                <Textarea
                  id="resolution_notes"
                  placeholder="Add any notes about this resolution..."
                  className="min-h-[100px]"
                  {...resolveForm.register('resolution_notes')}
                />
                <p className="text-xs text-gray-500 mt-1">
                  These notes will be visible to the dispute user
                </p>
              </div>
            </form>
          )}

          {/* Reject Form */}
          {actionType === 'reject' && (
            <form
              onSubmit={rejectForm.handleSubmit(handleReject)}
              className="space-y-4"
            >
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-red-900">
                      Rejecting Dispute
                    </h3>
                    <p className="text-sm text-red-700 mt-1">
                      This will reject the dispute claim and maintain the
                      current payment match status. The dispute user will be
                      notified of this decision.
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="reject_notes">Rejection Reason</Label>
                <Textarea
                  id="reject_notes"
                  placeholder="Explain why this dispute is being rejected..."
                  className="min-h-[100px]"
                  {...rejectForm.register('resolution_notes', {
                    required:
                      'Please provide a reason for rejecting this dispute',
                  })}
                />
                {rejectForm.formState.errors.resolution_notes && (
                  <p className="text-sm text-red-600 mt-1">
                    {rejectForm.formState.errors.resolution_notes.message}
                  </p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  This explanation will be sent to the dispute user
                </p>
              </div>
            </form>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <div className="space-x-2">
            {actionType === 'resolve' && (
              <Button
                onClick={resolveForm.handleSubmit(handleResolve)}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isLoading && <LoadingSpinner size="sm" className="mr-2" />}
                Resolve Dispute
              </Button>
            )}
            {actionType === 'reject' && (
              <Button
                onClick={rejectForm.handleSubmit(handleReject)}
                disabled={isLoading}
                variant="destructive"
              >
                {isLoading && <LoadingSpinner size="sm" className="mr-2" />}
                Reject Dispute
              </Button>
            )}
          </div>{' '}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DisputeResolveModal;
