// Withdraw types matching <PERSON><PERSON> backend
import type { Fund } from './fund';
import type { PaymentMethod } from './paymentMethod';
import type { PaymentMatch } from './paymentMatch';
import type { PaginationMeta } from './common';

export interface Withdraw {
  id: string;
  user_id: string;
  fund_id: string;
  base_withdrawable_amount: number;
  available_referral_bonus: number;
  withdrawable_referral_bonus: number;
  total_withdrawable_amount: number;
  amount_matched: number;
  status: 'pending' | 'matched' | 'completed';
  payment_method_id: string;
  created_at: string;
  updated_at: string;
  // Relationships
  fund?: Fund & {
    next_fund?: Fund;
  };
  payment_method?: PaymentMethod;
  payment_matches?: PaymentMatch[];

  // Computed properties
  total_to_fully_match?: number;
}

export interface CreateWithdrawData {
  fund_id: string;
}

export interface WithdrawFilters {
  status?: 'pending' | 'matched' | 'completed';
  currency?: 'fiat' | 'crypto';
  sort_field?: 'amount' | 'created_at' | 'status' | 'currency';
  sort_direction?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

export interface PaginatedWithdraws {
  withdraws: Withdraw[];
  pagination: PaginationMeta;
}

export interface WithdrawStatistics {
  overview: {
    total_count: number;
    count: {
      fiat: number;
      crypto: number;
    };
    amount: {
      fiat: number;
      crypto: number;
    };
  };
  statuses: {
    pending: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    matched: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    completed: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
  };
}
