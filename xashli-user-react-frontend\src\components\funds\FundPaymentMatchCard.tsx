import { Upload, Eye, Send, Flag } from 'lucide-react';
import { useState } from 'react';
import { Button } from '../ui/Button';
import { Badge } from '../ui';
import { getImageUrl } from '../../utils/images';
import { toTitleCase } from '../../utils/format';
import { DisputeModal } from '../disputes/DisputeModal';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { Fund } from '../../types/fund';
import type { PaymentDispute } from '../../types/dispute';

interface FundPaymentMatchCardProps {
  paymentMatch: PaymentMatch;
  currency: string;
  formatAmount: (amount: unknown, currency: string) => string;
  onPaymentMatchAction: (match: PaymentMatch, action: 'pay' | 'proof') => void;
  onPaystackAdminPayment?: (match: PaymentMatch) => void;
  fund?: Fund;
  index?: number; // Optional index for display purposes
  onDisputeCreated?: (dispute: PaymentDispute) => void;
}

export function FundPaymentMatchCard({
  paymentMatch,
  currency,
  formatAmount,
  onPaymentMatchAction,
  onPaystackAdminPayment,
  fund,
  index = 1,
  onDisputeCreated,
}: FundPaymentMatchCardProps) {
  const [isDisputeModalOpen, setIsDisputeModalOpen] = useState(false);

  // Check if there are any disputes
  const hasDisputes = paymentMatch.disputes && paymentMatch.disputes.length > 0;
  const canCreateDispute = paymentMatch.status === 'paid' && !hasDisputes;

  const handleDisputeCreated = (dispute: PaymentDispute) => {
    onDisputeCreated?.(dispute);
    setIsDisputeModalOpen(false);
  };

  const getDisputeStatusBadge = (status: string) => {
    const variants = {
      under_review: 'bg-warning/20 text-warning border-warning/30',
      resolved: 'bg-success/20 text-success border-success/30',
      rejected: 'bg-destructive/20 text-destructive border-destructive/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  const getPaymentMatchStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-warning/20 text-warning border-warning/30',
      paid: 'bg-primary/20 text-primary border-primary/30',
      confirmed: 'bg-success/20 text-success border-success/30',
      completed: 'bg-success/20 text-success border-success/30',
      disputed: 'bg-destructive/20 text-destructive border-destructive/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  const handleSendPayment = () => {
    // Check if this is an admin withdrawal with fiat currency
    if (paymentMatch.is_admin_withdraw && fund?.currency === 'fiat') {
      // Use Paystack for admin withdrawals
      onPaystackAdminPayment?.(paymentMatch);
    } else {
      // Use existing flow for regular payments
      onPaymentMatchAction(paymentMatch, 'pay');
    }
  };

  return (
    <div className='bg-background-tertiary rounded-lg border border-border p-4'>
      {/* Status badge at the top */}
      <div className='mb-3 flex justify-start items-center gap-2'>
        <Badge className={getPaymentMatchStatusBadge(paymentMatch.status)}>
          {toTitleCase(paymentMatch.status)}
        </Badge>
        {hasDisputes && (
          <Badge
            className={getDisputeStatusBadge(paymentMatch.disputes![0].status)}
          >
            {paymentMatch.disputes!.length > 1
              ? `${paymentMatch.disputes!.length} Disputes`
              : toTitleCase(paymentMatch.disputes![0].status)}
          </Badge>
        )}
      </div>

      {/* Match info header */}
      <div className='flex items-center justify-between mb-3'>
        <span className='text-sm font-medium text-foreground'>
          Match #{index}
        </span>
        <div className='text-sm font-semibold text-foreground'>
          {formatAmount(paymentMatch.amount || 0, currency)}
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
        <div>
          <p className='text-sm text-foreground-secondary mb-1'>Match ID</p>
          <p className='text-sm font-mono text-foreground'>
            {paymentMatch.id.slice(0, 8)}...
          </p>
        </div>
        <div>
          <p className='text-sm text-foreground-secondary mb-1'>Created</p>
          <p className='text-sm text-foreground'>
            {new Date(paymentMatch.created_at).toLocaleDateString()}
          </p>
        </div>
      </div>

      {/* Action buttons for the match */}
      <div className='flex gap-2 flex-wrap'>
        {paymentMatch.status === 'pending' && (
          <Button
            size='sm'
            onClick={handleSendPayment}
            className='flex items-center gap-2'
          >
            <Send className='h-4 w-4' />
            Send Payment
          </Button>
        )}

        {(paymentMatch.status === 'paid' ||
          paymentMatch.status === 'confirmed') &&
          !paymentMatch.payment_proof_image && (
            <Button
              size='sm'
              variant='outline'
              onClick={() => onPaymentMatchAction(paymentMatch, 'proof')}
              className='flex items-center gap-2'
            >
              <Upload className='h-4 w-4' />
              Upload Payment Proof
            </Button>
          )}

        {paymentMatch.payment_proof_image && (
          <Button
            size='sm'
            variant='outline'
            onClick={() =>
              window.open(
                getImageUrl(paymentMatch.payment_proof_image!)!,
                '_blank'
              )
            }
            className='flex items-center gap-2'
          >
            <Eye className='h-4 w-4' />
            View Payment Proof
          </Button>
        )}

        {/* Dispute button */}
        {canCreateDispute && (
          <Button
            size='sm'
            variant='outline'
            onClick={() => setIsDisputeModalOpen(true)}
            className='flex items-center gap-2 text-destructive border-destructive/30 hover:bg-destructive hover:text-white'
          >
            <Flag className='h-4 w-4' />
            Dispute Payment
          </Button>
        )}

        {/* View disputes button */}
        {hasDisputes && !canCreateDispute && (
          <Button
            size='sm'
            variant='outline'
            onClick={() => setIsDisputeModalOpen(true)}
            className='flex items-center gap-2 text-destructive border-destructive/30 hover:bg-destructive hover:text-white'
          >
            <Flag className='h-4 w-4' />
            {paymentMatch.disputes!.length > 1
              ? `View Disputes (${paymentMatch.disputes!.length})`
              : 'View Dispute'}
          </Button>
        )}
      </div>

      {/* Dispute Modal */}
      <DisputeModal
        isOpen={isDisputeModalOpen}
        onClose={() => setIsDisputeModalOpen(false)}
        paymentMatch={paymentMatch}
        onDisputeCreated={handleDisputeCreated}
      />
    </div>
  );
}
