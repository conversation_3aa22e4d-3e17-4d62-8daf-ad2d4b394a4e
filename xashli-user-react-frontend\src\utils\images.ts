/**
 * Utility functions for handling image URLs
 */
export const getImageUrl = (
  imagePath: string | null | undefined
): string | null => {
  if (!imagePath) {
    return null;
  }

  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  const apiBaseUrl =
    import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
  const baseServerUrl = apiBaseUrl.replace('/api', '');

  // Handle different path formats
  let cleanImagePath = imagePath.startsWith('/')
    ? imagePath.substring(1)
    : imagePath;

  // If the path is missing the 'uploads/' prefix, add it
  if (cleanImagePath.startsWith('storage/profile_images/')) {
    cleanImagePath = cleanImagePath.replace(
      'storage/profile_images/',
      'storage/uploads/profile_images/'
    );
  }
  const fullUrl = `${baseServerUrl}/${cleanImagePath}`;

  return fullUrl;
};

export const getImageUrlWithFallback = (
  imagePath: string | null | undefined,
  fallback: string = ''
): string => {
  return getImageUrl(imagePath) || fallback;
};
