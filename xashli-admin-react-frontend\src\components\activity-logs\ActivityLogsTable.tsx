import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FileText, Eye, Clock, User, Activity } from 'lucide-react';
import { Button } from '../ui/button';
import { LoadingSpinner } from '../ui/loading';
import { Badge } from '../ui/badge';
import { formatDate } from '../../utils';
import type { AdminActivityLog, ActionType } from '../../types';

interface ActivityLogsTableProps {
  logs: AdminActivityLog[];
  isLoading: boolean;
}

export const ActivityLogsTable: React.FC<ActivityLogsTableProps> = ({
  logs,
  isLoading,
}) => {
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (logs.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No activity logs found
        </h3>
        <p className="text-gray-500">
          No admin activities match your current filters.
        </p>
      </div>
    );
  }

  const getActionBadge = (actionType: ActionType) => {
    const badgeMap = {
      user_created: 'bg-green-100 text-green-800',
      user_updated: 'bg-blue-100 text-blue-800',
      user_deleted: 'bg-red-100 text-red-800',
      dispute_resolved: 'bg-green-100 text-green-800',
      dispute_rejected: 'bg-red-100 text-red-800',
      payment_manually_matched: 'bg-purple-100 text-purple-800',
      auto_match_triggered: 'bg-orange-100 text-orange-800',
      settings_created: 'bg-green-100 text-green-800',
      settings_updated: 'bg-blue-100 text-blue-800',
      settings_deleted: 'bg-red-100 text-red-800',
      platform_fee_collected: 'bg-yellow-100 text-yellow-800',
    };

    return badgeMap[actionType] || 'bg-gray-100 text-gray-800';
  };

  const formatActionType = (actionType: ActionType) => {
    return actionType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Action
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Admin
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Target User
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date & Time
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {logs.map(log => (
            <tr key={log.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <Badge
                      className={`${getActionBadge(log.action_type)} font-medium`}
                    >
                      {formatActionType(log.action_type)}
                    </Badge>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-brand-gold-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xs font-medium text-black">
                      {log.admin?.full_name?.charAt(0).toUpperCase() || 'A'}
                    </span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {log.admin?.full_name || 'Unknown Admin'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {log.admin?.email}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {log.targetUser ? (
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {log.targetUser.full_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {log.targetUser.email}
                      </div>
                    </div>
                  </div>
                ) : (
                  <span className="text-sm text-gray-500">-</span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-gray-400 mr-2" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {formatDate(log.created_at)}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/activity-logs/${log.id}`)}
                  className="text-brand-gold-600 hover:text-brand-gold-700"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
