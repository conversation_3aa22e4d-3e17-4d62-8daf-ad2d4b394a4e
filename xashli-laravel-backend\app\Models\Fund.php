<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Fund extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'amount',
        'currency',
        'payment_method_id',
        'platform_fee_percentage',
        'start_date',
        'maturity_date',
        'maturity_days',
        'growth_percentage',
        'growth_amount',
        'referral_bonus_limit',
        'is_eligible_for_withdrawal',
        'amount_matched',
        'status',
        'prev_fund_id',
        'next_fund_id',
        'withdraw_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:8',
        'platform_fee_percentage' => 'decimal:2',
        'growth_percentage' => 'decimal:2',
        'growth_amount' => 'decimal:8',
        'referral_bonus_limit' => 'decimal:8',
        'is_eligible_for_withdrawal' => 'boolean',
        'start_date' => 'datetime',
        'maturity_date' => 'datetime',
        'maturity_days' => 'integer',
        'amount_matched' => 'decimal:8',
        'status' => 'string',
        'currency' => 'string',
    ];

    /**
     * Get the user that owns the fund.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment method for the fund.
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Get the withdraws for the fund.
     */
    public function withdraws(): HasMany
    {
        return $this->hasMany(Withdraw::class);
    }

    /**
     * Get the user withdraw for the fund.
     * Note: This is a soft reference without foreign key constraint.
     */
    public function userWithdraw(): BelongsTo
    {
        return $this->belongsTo(Withdraw::class, 'withdraw_id');
    }

    /**
     * Get the payment matches for the fund.
     */
    public function paymentMatches(): HasMany
    {
        return $this->hasMany(PaymentMatch::class);
    }

    /**
     * Get the referral bonuses for the fund.
     */
    public function referralBonuses(): HasMany
    {
        return $this->hasMany(ReferralBonus::class);
    }

    /**
     * Get the previous fund.
     */
    public function previousFund(): BelongsTo
    {
        return $this->belongsTo(Fund::class, 'prev_fund_id');
    }

    /**
     * Get the next fund.
     */
    public function nextFund(): BelongsTo
    {
        return $this->belongsTo(Fund::class, 'next_fund_id');
    }

    /**
     * Check if the fund has a next fund linked.
     */
    public function hasNextFund(): bool
    {
        return $this->next_fund_id !== null;
    }

    /**
     * Check if the fund is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the fund is matched.
     */
    public function isMatched(): bool
    {
        return $this->status === 'matched';
    }

    /**
     * Check if the fund is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the fund is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the fund is for fiat.
     */
    public function isFiat(): bool
    {
        return $this->currency === 'fiat';
    }

    /**
     * Check if the fund is for crypto.
     */
    public function isCrypto(): bool
    {
        return $this->currency === 'crypto';
    }

    /**
     * Check if the fund is eligible for withdrawal.
     */
    public function isEligibleForWithdrawal(): bool
    {
        return $this->is_eligible_for_withdrawal;
    }

    /**
     * Check if the fund is matured.
     */
    public function isMatured(): bool
    {
        return now()->gte($this->maturity_date);
    }

    /**
     * Check if the fund can be withdrawn.
     * A fund can be withdrawn if it's matured, has a next fund that's completed, and hasn't been withdrawn yet.
     */
    public function canBeWithdrawn(): bool
    {
        return $this->isMatured() &&
               $this->hasNextFund() &&
               $this->nextFund?->isCompleted() &&
               $this->withdraw_id === null;
    }

    /**
     * Check if the fund has a user withdrawal.
     */
    public function hasUserWithdraw(): bool
    {
        return $this->withdraw_id !== null;
    }

    /**
     * Check if all payment matches are settled (confirmed or completed).
     */
    public function arePaymentMatchesSettled(): bool
    {
        return $this->paymentMatches()
            ->whereNotIn('status', ['confirmed', 'completed'])
            ->count() === 0;
    }

    /**
     * Calculate the total return percentage (growth + max referral bonus).
     */
    public function getTotalReturnPercentage(): float
    {
        $growthPercentage = $this->growth_amount / $this->amount;
        $referralBonusPercentage = $this->referral_bonus_limit / $this->amount;

        return $growthPercentage + $referralBonusPercentage;
    }

    /**
     * Check if the total return exceeds 100% of the fund amount.
     */
    public function totalReturnExceedsLimit(): bool
    {
        return $this->getTotalReturnPercentage() > 1.0;
    }

    /**
     * Check if the fund has at least one confirmed payment match.
     */
    public function hasConfirmedPayments(): bool
    {
        return $this->paymentMatches()->where('is_payment_sent_confirmed', true)->exists();
    }

    /**
     * Get the platform fees for the fund.
     */
    public function platformFees(): HasMany
    {
        return $this->hasMany(PlatformFee::class);
    }

    /**
     * Check if platform fee has been collected for this fund.
     */
    public function hasPlatformFeeCollected(): bool
    {
        return $this->platformFees()->exists();
    }

    /**
     * Calculate platform fee amount for this fund.
     */
    public function calculatePlatformFeeAmount(): float
    {
        return $this->amount * ($this->platform_fee_percentage / 100);
    }
}
