<?php

namespace App\Services;

use App\Enums\ElitePrivilegeLevel;
use App\Models\Fund;
use App\Models\PlatformSetting;
use App\Models\User;

class UserPrivilegeService
{
    /**
     * Calculate and update user privileges based on current criteria.
     */
    public function calculateAndUpdatePrivileges(User $user): void
    {
        $hasPremiumPrivilege = $this->calculatePremiumPrivilege($user);
        $hasElitePrivilege = $this->calculateElitePrivilege($user);
        $elitePrivilegeLevel = $this->calculateElitePrivilegeLevel($user);

        // Update user privileges if they've changed
        if ($user->has_premium_privilege !== $hasPremiumPrivilege ||
            $user->has_elite_privilege !== $hasElitePrivilege ||
            $user->elite_privilege_level !== $elitePrivilegeLevel->value) {

            $user->update([
                'has_premium_privilege' => $hasPremiumPrivilege,
                'has_elite_privilege' => $hasElitePrivilege,
                'elite_privilege_level' => $elitePrivilegeLevel->value,
            ]);
        }
    }

    /**
     * Check if user qualifies for premium privilege (fast maturity).
     * Premium privilege is granted when total L1 referee funding meets threshold.
     */
    public function calculatePremiumPrivilege(User $user): bool
    {
        // Early return if user already has premium privilege
        if ($user->hasPremiumPrivilege()) {
            return true;
        }

        // Check both fiat and crypto thresholds
        $fiatThreshold = PlatformSetting::getSetting('fiat_fast_maturity_threshold');
        $cryptoThreshold = PlatformSetting::getSetting('crypto_fast_maturity_threshold');

        $totalFiatFunding = $user->totalLevel1RefereeFunding('fiat');
        $totalCryptoFunding = $user->totalLevel1RefereeFunding('crypto');

        // User has premium privilege if they meet either threshold
        return $totalFiatFunding >= $fiatThreshold || $totalCryptoFunding >= $cryptoThreshold;
    }

    /**
     * Check if user qualifies for elite privilege (enhanced fund limits).
     * Elite privilege is granted when user meets either of two criteria:
     * 1. Has 10 consecutive successful transactions without defaulting
     * 2. Has 10 active level-1 referees who completed at least 1 transaction cycle each
     */
    public function calculateElitePrivilege(User $user): bool
    {
        // Early return if user already has elite privilege
        if ($user->hasElitePrivilege()) {
            return true;
        }

        return $this->hasEliteTransactionStreak($user) || $this->hasEliteActiveReferees($user);
    }

    /**
     * Calculate the user's elite privilege level based on transaction streaks or active referees.
     * Returns the highest level the user qualifies for.
     */
    public function calculateElitePrivilegeLevel(User $user): ElitePrivilegeLevel
    {
        // Early return if user already has the highest elite level
        if ($user->getElitePrivilegeLevel() === ElitePrivilegeLevel::LEVEL_4) {
            return ElitePrivilegeLevel::LEVEL_4;
        }

        // Check from highest to lowest level
        for ($level = 4; $level >= 1; $level--) {
            if ($this->qualifiesForEliteLevel($user, $level)) {
                return ElitePrivilegeLevel::from($level);
            }
        }

        return ElitePrivilegeLevel::NONE;
    }

    /**
     * Check if user qualifies for a specific elite level.
     */
    private function qualifiesForEliteLevel(User $user, int $level): bool
    {
        $eliteLevel = ElitePrivilegeLevel::from($level);
        $transactionThreshold = PlatformSetting::getSetting($eliteLevel->getTransactionThresholdSettingKey());
        $refereeThreshold = PlatformSetting::getSetting($eliteLevel->getRefereeThresholdSettingKey());

        return $this->hasEliteTransactionStreak($user, $transactionThreshold) ||
               $this->hasEliteActiveReferees($user, $refereeThreshold);
    }

    /**
     * Check if user has the required number of consecutive successful transactions.
     * A successful transaction means:
     * 1. Uploaded proof of payment for each match
     * 2. Confirmed payment sent within an hour after match is created
     * 3. Payment matches for the funds are confirmed by receiver
     */
    public function hasEliteTransactionStreak(User $user, int $threshold = 10): bool
    {
        // Get user's completed funds ordered by completion date (most recent first)
        $recentFunds = Fund::where('user_id', $user->id)
            ->where('status', 'completed')
            ->with(['paymentMatches'])
            ->orderBy('created_at', 'desc')
            ->limit($threshold)
            ->get();

        // Need at least the required number of completed funds
        if ($recentFunds->count() < $threshold) {
            return false;
        }

        // Check each of the recent funds for successful transaction criteria
        foreach ($recentFunds as $fund) {
            if (! $this->areFundPaymentMatchesSuccessful($fund)) {
                return false; // Failed the streak
            }
        }

        return true; // All recent transactions were successful
    }

    /**
     * Check if user has the required number of active level-1 referees with completed transaction cycles.
     * A referee has completed a transaction cycle if they have at least one completed fund
     * with all payment matches confirmed.
     */
    public function hasEliteActiveReferees(User $user, int $threshold = 10): bool
    {
        $activeRefereesWithCompletedCycles = $user->referees()
            ->where('is_active', true)
            ->whereHas('funds', function ($query) {
                $query->where('status', 'completed')
                    ->whereHas('paymentMatches', function ($matchQuery) {
                        $matchQuery->where('status', 'confirmed');
                    });
            })
            ->count();

        return $activeRefereesWithCompletedCycles >= $threshold;
    }

    /**
     * Get maturity days based on user privileges.
     */
    public function getMaturityDays(User $user): int
    {
        if ($user->has_premium_privilege) {
            return (int) PlatformSetting::getSetting('fast_maturity_days');
        }

        return (int) PlatformSetting::getSetting('standard_maturity_days');
    }

    /**
     * Get maximum fund amount based on user privileges.
     */
    public function getMaximumFundAmount(User $user, string $currency): float
    {
        $baseSetting = $currency === 'fiat'
            ? 'maximum_fiat_fund_amount'
            : 'maximum_crypto_fund_amount';

        $baseAmount = PlatformSetting::getSetting($baseSetting);

        // Apply elite privilege multiplier based on user's elite level
        $eliteLevel = $user->getElitePrivilegeLevel();

        if ($eliteLevel !== ElitePrivilegeLevel::NONE) {
            $multiplier = $this->getEliteMaximumFundMultiplier($eliteLevel);

            return $baseAmount * $multiplier;
        }

        return $baseAmount;
    }

    /**
     * Get the maximum fund multiplier for a specific elite level.
     */
    private function getEliteMaximumFundMultiplier(ElitePrivilegeLevel $level): float
    {
        if ($level === ElitePrivilegeLevel::NONE) {
            return 1.0;
        }

        $settingKey = $level->getMaximumFundMultiplierSettingKey();

        return PlatformSetting::getSetting($settingKey) ?? 1.0;
    }

    /**
     * Check if a fund's payment matches meet the elite privilege criteria.
     * A successful transaction requires:
     * 1. All payment matches have proof uploaded
     * 2. Payment sent confirmed within 1 hour of match creation
     * 3. All payment matches confirmed by receiver
     */
    private function areFundPaymentMatchesSuccessful(Fund $fund): bool
    {
        $paymentMatches = $fund->paymentMatches;

        // Fund must have at least one payment match
        if ($paymentMatches->isEmpty()) {
            return false;
        }

        foreach ($paymentMatches as $match) {
            // Check if proof was uploaded
            if (empty($match->payment_proof_image)) {
                return false;
            }

            // Check if payment was sent and confirmed
            if (! $match->is_payment_sent_confirmed || ! $match->payment_sent_confirmed_at) {
                return false;
            }

            // Check if payment was confirmed within 1 hour of match creation
            $timeDiff = $match->payment_sent_confirmed_at->diffInHours($match->created_at);
            if ($timeDiff > 1) {
                return false;
            }

            // Check if payment was received and confirmed
            if (! $match->is_payment_received_confirmed) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get a summary of user's privilege status.
     */
    public function getPrivilegeSummary(User $user): array
    {
        $eliteLevel = $user->getElitePrivilegeLevel();

        return [
            'has_premium_privilege' => $user->hasPremiumPrivilege(),
            'has_elite_privilege' => $user->hasElitePrivilege(),
            'elite_privilege_level' => $eliteLevel->value,
            'elite_privilege_level_name' => $eliteLevel->getName(),
            'has_any_elite_privilege' => $user->hasAnyElitePrivilege(),
            'maturity_days' => $this->getMaturityDays($user),
            'maximum_fiat_fund_amount' => $this->getMaximumFundAmount($user, 'fiat'),
            'maximum_crypto_fund_amount' => $this->getMaximumFundAmount($user, 'crypto'),
            'elite_maximum_fund_multiplier' => $this->getEliteMaximumFundMultiplier($eliteLevel),
        ];
    }
}
