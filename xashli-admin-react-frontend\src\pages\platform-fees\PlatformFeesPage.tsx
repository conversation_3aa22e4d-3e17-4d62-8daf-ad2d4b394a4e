import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Coins, Filter, Banknote, Users } from 'lucide-react';
import { platformFeeService } from '@/services/platformFeeService';
import type {
  PlatformFee,
  PlatformFeeFilters,
  PlatformFeeStatistics,
} from '@/types/platformFee';
import { formatCurrencyAmount, formatDate } from '@/utils/format';
import { LoadingSpinner } from '@/components/ui/loading';
import { Pagination } from '@/components/ui/pagination';
import { MainLayout } from '@/components/layout';

interface PlatformFeesPageProps {}

export const PlatformFeesPage: React.FC<PlatformFeesPageProps> = () => {
  const [platformFees, setPlatformFees] = useState<PlatformFee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PlatformFeeFilters>({
    page: 1,
    per_page: 20,
  });
  const [pendingFilters, setPendingFilters] = useState<PlatformFeeFilters>({
    page: 1,
    per_page: 20,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    total: 0,
    per_page: 20,
  });

  // Statistics data - using direct backend structure
  const [statisticsData, setStatisticsData] = useState<PlatformFeeStatistics>({
    counts: {
      total: 0,
      fiat: 0,
      crypto: 0,
    },
    amounts: {
      total: {
        fiat: 0,
        crypto: 0,
      },
    },
  });

  useEffect(() => {
    fetchPlatformFees();
    fetchStatistics();
  }, [filters]);

  // Initialize pending filters with current filters
  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  const fetchPlatformFees = async () => {
    try {
      setLoading(true);
      const response = await platformFeeService.getFees(filters);
      if (response.data) {
        setPlatformFees(response.data.platformFees);
        setPagination({
          current_page: response.data.pagination.current_page,
          last_page: response.data.pagination.last_page,
          total: response.data.pagination.total,
          per_page: response.data.pagination.per_page,
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch platform fee collections');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await platformFeeService.getStatistics();
      if (response.data) {
        // Use the direct statistics response structure
        setStatisticsData(response.data);
      }
    } catch (err) {
      console.error('Failed to fetch statistics data:', err);
    }
  };

  const handleFilterChange = (key: keyof PlatformFeeFilters, value: any) => {
    setPendingFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const applyFilters = () => {
    setFilters({
      ...pendingFilters,
      page: 1, // Reset to first page when filters change
    });
  };

  const clearFilters = () => {
    const clearedFilters = { page: 1, per_page: 20 };
    setFilters(clearedFilters);
    setPendingFilters(clearedFilters);
  };

  const hasFilterChanges = () => {
    const { page, per_page, ...currentFiltersWithoutPagination } = filters;
    const {
      page: pendingPage,
      per_page: pendingPerPage,
      ...pendingFiltersWithoutPagination
    } = pendingFilters;
    return (
      JSON.stringify(currentFiltersWithoutPagination) !==
      JSON.stringify(pendingFiltersWithoutPagination)
    );
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setFilters(prev => ({ ...prev, per_page: pageSize, page: 1 }));
  };

  const getCurrencyBadgeColor = (currency: string) => {
    return currency === 'fiat'
      ? 'bg-green-100 text-green-800'
      : 'bg-blue-100 text-blue-800';
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Platform Fee Collections
            </h1>
            <p className="text-muted-foreground">
              Monitor and track all platform fee collections
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>
        </div>{' '}
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Fiat Fees
              </CardTitle>
              <Banknote className="h-4 w-4 text-muted-foreground" />{' '}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrencyAmount(
                  statisticsData.amounts.total.fiat,
                  'fiat'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                All time fiat collections
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Crypto Fees
              </CardTitle>
              <Coins className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrencyAmount(
                  statisticsData.amounts.total.crypto,
                  'crypto'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                All time crypto collections
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Collections
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statisticsData.counts.total.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                All time collections
              </p>
            </CardContent>
          </Card>
        </div>
        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Currency
                  </label>
                  <Select
                    value={pendingFilters.currency || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'currency',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All currencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Currencies</SelectItem>
                      <SelectItem value="fiat">Fiat (NGN)</SelectItem>
                      <SelectItem value="crypto">Crypto (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Start Date
                  </label>
                  <Input
                    type="date"
                    value={pendingFilters.start_date || ''}
                    onChange={e =>
                      handleFilterChange('start_date', e.target.value)
                    }
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    End Date
                  </label>
                  <Input
                    type="date"
                    value={pendingFilters.end_date || ''}
                    onChange={e =>
                      handleFilterChange('end_date', e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Buttons container - separate div below input fields */}
              <div className="flex justify-end gap-3 mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Collections Table */}
        <Card>
          <CardHeader>
            <CardTitle>
              Fee Collections ({pagination.total.toLocaleString()})
            </CardTitle>
          </CardHeader>{' '}
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-600">{error}</div>
            ) : platformFees.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No fee collections found
              </div>
            ) : (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-4">Fund</th>
                        <th className="text-left p-4">User</th>
                        <th className="text-left p-4">Amount</th>
                        <th className="text-left p-4">Fee</th>
                        <th className="text-left p-4">Currency</th>
                        <th className="text-left p-4">Admin</th>
                        <th className="text-left p-4">Collected At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {platformFees.map(platformFee => (
                        <tr
                          key={platformFee.id}
                          className="border-b hover:bg-muted/50"
                        >
                          <td className="p-4">
                            <div className="font-mono text-sm">
                              {platformFee.fund_id.slice(0, 8)}...
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Fund:{' '}
                              {formatCurrencyAmount(
                                platformFee.fund_amount,
                                platformFee.currency
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium">
                              {platformFee.fund?.user?.full_name || 'Unknown'}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {platformFee.fund?.user?.email}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium">
                              {formatCurrencyAmount(
                                platformFee.fund_amount,
                                platformFee.currency
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium">
                              {formatCurrencyAmount(
                                platformFee.fee_amount,
                                platformFee.currency
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {platformFee.fee_percentage}%
                            </div>
                          </td>
                          <td className="p-4">
                            <Badge
                              className={getCurrencyBadgeColor(
                                platformFee.currency
                              )}
                            >
                              {platformFee.currency.toUpperCase()}
                            </Badge>
                          </td>
                          <td className="p-4">
                            <div className="font-medium">
                              {platformFee.admin?.full_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {platformFee.admin?.email}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="text-sm">
                              {formatDate(platformFee.collected_at)}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>{' '}
                {/* Pagination */}
                <Pagination
                  pagination={{
                    current_page: pagination.current_page,
                    last_page: pagination.last_page,
                    per_page: pagination.per_page,
                    total: pagination.total,
                    from: null,
                    to: null,
                  }}
                  currentPage={pagination.current_page}
                  pageSize={pagination.per_page}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  itemLabel="Platform Fees"
                />
              </div>
            )}{' '}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};
