import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { MainLayout } from '../../components/layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Label,
  Switch,
  Alert,
  Separator,
  Badge,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui';
import {
  Save,
  RefreshCw,
  Info,
  AlertTriangle,
  Loader2,
  Check,
} from 'lucide-react';
import {
  usePlatformSettings,
  useUpdatePlatformSetting,
} from '../../hooks/usePlatformSettings';
import {
  PLATFORM_SETTINGS_CONFIG,
  formatSettingValue,
} from '../../config/platformSettings';

// Individual Setting Component
interface SettingItemProps {
  configSetting: any;
  currentSetting: any;
  updateMutation: any;
}

const SettingItem: React.FC<SettingItemProps> = ({
  configSetting,
  currentSetting,
  updateMutation,
}) => {
  const [value, setValue] = useState(currentSetting?.value || '');
  const [type, setType] = useState(currentSetting?.type || configSetting.type);
  const [isUpdating, setIsUpdating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Reset value and type when currentSetting changes
  React.useEffect(() => {
    setValue(currentSetting?.value || '');
    setType(currentSetting?.type || configSetting.type);
    setHasChanges(false);
  }, [currentSetting?.value, currentSetting?.type, configSetting.type]);

  const handleValueChange = (newValue: any) => {
    setValue(newValue);
    checkForChanges(newValue, type);
  };

  const handleTypeChange = (newType: string) => {
    setType(newType as 'string' | 'int' | 'decimal' | 'boolean');
    // Convert value based on new type
    let convertedValue = value;
    if (newType === 'boolean') {
      convertedValue = Boolean(value);
    } else if (newType === 'int') {
      convertedValue = parseInt(value.toString()) || 0;
    } else if (newType === 'decimal') {
      convertedValue = parseFloat(value.toString()) || 0;
    } else {
      convertedValue = value.toString();
    }
    setValue(convertedValue);
    checkForChanges(
      convertedValue,
      newType as 'string' | 'int' | 'decimal' | 'boolean'
    );
  };

  const checkForChanges = (newValue: any, newType: string) => {
    const valueChanged = newValue !== currentSetting?.value;
    const typeChanged = newType !== currentSetting?.type;
    setHasChanges(valueChanged || typeChanged);
  };

  const handleUpdate = async () => {
    if (!currentSetting || !hasChanges) return;

    setIsUpdating(true);
    try {
      const updateData: any = { value: value.toString() };

      // Include type if it has changed
      if (type !== currentSetting.type) {
        updateData.type = type;
      }

      await updateMutation.mutateAsync({
        id: currentSetting.id,
        data: updateData,
      });
      setHasChanges(false);
      setLastSaved(new Date());
    } catch (error) {
      // Error handling is done by the mutation
    } finally {
      setIsUpdating(false);
    }
  };

  // Get the current effective type for input rendering
  const effectiveType = type || configSetting.type;

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-gray-50/50">
      <div className="flex items-start justify-between">
        <div className="space-y-1 flex-1">
          <div className="flex items-center space-x-2">
            <Label
              htmlFor={configSetting.key}
              className="text-base font-medium"
            >
              {configSetting.title}
            </Label>
            {configSetting.validation?.required && (
              <Badge variant="outline" className="text-xs">
                Required
              </Badge>
            )}
            <Badge variant="secondary" className="text-xs">
              Type: {effectiveType}
            </Badge>
          </div>
          <p className="text-sm text-gray-600">{configSetting.description}</p>
          {currentSetting && (
            <p className="text-xs text-gray-500">
              Current:{' '}
              {formatSettingValue(currentSetting.value, {
                ...configSetting,
                type: currentSetting.type,
              })}
            </p>
          )}
          {lastSaved && (
            <p className="text-xs text-green-600 flex items-center space-x-1">
              <Check className="h-3 w-3" />
              <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
            </p>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {/* Type Selector */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Data Type</Label>
          <Select
            value={effectiveType}
            onValueChange={handleTypeChange}
            disabled={isUpdating}
          >
            <SelectTrigger className="max-w-xs">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="string">String (Text)</SelectItem>
              <SelectItem value="int">Integer (Whole Number)</SelectItem>
              <SelectItem value="decimal">
                Decimal (Number with decimals)
              </SelectItem>
              <SelectItem value="boolean">Boolean (True/False)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Value Input */}
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <Label className="text-sm font-medium">Value</Label>
            <div className="mt-1">
              {effectiveType === 'boolean' ? (
                <div className="flex items-center space-x-2">
                  <Switch
                    id={configSetting.key}
                    checked={Boolean(value)}
                    onCheckedChange={handleValueChange}
                    disabled={isUpdating}
                  />
                  <Label htmlFor={configSetting.key} className="text-sm">
                    {Boolean(value) ? 'Enabled' : 'Disabled'}
                  </Label>
                </div>
              ) : (
                <div className="space-y-2">
                  <Input
                    id={configSetting.key}
                    type={
                      effectiveType === 'decimal'
                        ? 'number'
                        : effectiveType === 'int'
                          ? 'number'
                          : 'text'
                    }
                    step={
                      configSetting.validation?.step ||
                      (effectiveType === 'decimal'
                        ? '0.01'
                        : effectiveType === 'int'
                          ? '1'
                          : undefined)
                    }
                    min={configSetting.validation?.min}
                    max={configSetting.validation?.max}
                    placeholder={`Enter ${configSetting.title.toLowerCase()}`}
                    value={value?.toString() || ''}
                    onChange={e => {
                      const newValue =
                        effectiveType === 'string'
                          ? e.target.value
                          : parseFloat(e.target.value) || 0;
                      handleValueChange(newValue);
                    }}
                    className="max-w-xs"
                    disabled={isUpdating}
                  />

                  {configSetting.unit && (
                    <p className="text-xs text-gray-500">
                      Unit: {configSetting.unit}
                    </p>
                  )}

                  {configSetting.validation && (
                    <div className="text-xs text-gray-500 space-y-1">
                      {configSetting.validation.min !== undefined && (
                        <p>Minimum: {configSetting.validation.min}</p>
                      )}
                      {configSetting.validation.max !== undefined && (
                        <p>Maximum: {configSetting.validation.max}</p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          <Button
            onClick={handleUpdate}
            disabled={!hasChanges || isUpdating}
            size="sm"
            className="bg-brand-gold-500 hover:bg-brand-gold-600 text-black disabled:opacity-50"
          >
            {isUpdating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isUpdating ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export const SettingsCategoryPage: React.FC = () => {
  const { category: categoryKey } = useParams<{ category: string }>();
  const queryClient = useQueryClient();

  // Find the category configuration
  const category = PLATFORM_SETTINGS_CONFIG.find(c => c.key === categoryKey);
  // Get all platform settings
  const { data: settingsResponse, isLoading, error } = usePlatformSettings();

  // Filter settings for this category
  const categorySettings =
    settingsResponse?.data?.filter((setting: any) =>
      category?.settings.some(
        configSetting => configSetting.key === setting.key
      )
    ) || [];

  // Update mutation
  const updateMutation = useUpdatePlatformSetting();
  if (!category) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <div>
              <h4 className="font-medium">Category Not Found</h4>
              <p className="text-sm text-gray-600 mt-1">
                The requested settings category could not be found.
              </p>
            </div>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-brand-gold-500" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <div>
              <h4 className="font-medium">Error Loading Settings</h4>
              <p className="text-sm text-gray-600 mt-1">
                Failed to load platform settings. Please try again.
              </p>
            </div>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  const IconComponent = category.icon;

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-brand-gold-100 rounded-lg">
              <IconComponent className="h-6 w-6 text-brand-gold-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {category.title}
              </h1>
              <p className="text-gray-600">{category.description}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant="outline">{category.settings.length} settings</Badge>
            <Button
              onClick={() =>
                queryClient.invalidateQueries({
                  queryKey: ['platform-settings'],
                })
              }
              variant="outline"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Settings List */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
            <CardDescription>
              Each setting can be updated individually. Click "Save" next to any
              setting to apply changes.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {category.settings.map((configSetting, index) => {
              const currentSetting = categorySettings.find(
                s => s.key === configSetting.key
              );

              return (
                <React.Fragment key={configSetting.key}>
                  {index > 0 && <Separator className="my-6" />}
                  <SettingItem
                    configSetting={configSetting}
                    currentSetting={currentSetting}
                    updateMutation={updateMutation}
                  />
                </React.Fragment>
              );
            })}
          </CardContent>
        </Card>

        {/* Info Section */}
        <Alert>
          <Info className="h-4 w-4" />
          <div>
            <p className="text-sm">
              Changes are applied immediately when you save individual settings.
              Each setting operates independently and updates will take effect
              right away.
            </p>
          </div>
        </Alert>
      </div>
    </MainLayout>
  );
};
