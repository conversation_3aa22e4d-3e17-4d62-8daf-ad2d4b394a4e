import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Eye,
  Filter,
  RefreshCw,
  <PERSON>C<PERSON>,
  TrendingUp,
  TrendingDown,
  X,
} from 'lucide-react';
import { paymentMatchService } from '@/services/paymentMatch';
import { fundService } from '@/services/fund';
import { withdrawService } from '@/services/withdraw';
import { type PaymentMatchFilters } from '@/types/paymentMatch';
import { MainLayout } from '@/components/layout';
import { Pagination } from '../../components/ui/pagination';
import { LoadingSpinner } from '../../components/ui/loading';
import {
  formatCurrencyAmount,
  formatDate,
  shortenId,
} from '../../utils/format';
import type { Currency } from '../../types/common';

const PaymentMatchesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [filters, setFilters] = useState<PaymentMatchFilters>({});
  const [pendingFilters, setPendingFilters] = useState<PaymentMatchFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Initialize filters from URL search params
  useEffect(() => {
    const fundId = searchParams.get('fund_id');
    const withdrawId = searchParams.get('withdraw_id');

    if (fundId || withdrawId) {
      const initialFilters: PaymentMatchFilters = {};
      if (fundId) initialFilters.fund_id = fundId;
      if (withdrawId) initialFilters.withdraw_id = withdrawId;

      setFilters(initialFilters);
      setPendingFilters(initialFilters);
      setShowFilters(true); // Show filters when fund_id or withdraw_id is present
    }
  }, [searchParams]);

  // Initialize pending filters with current filters
  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  // Fetch payment matches with current filters and pagination
  const {
    data: paymentMatchesResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['payment-matches', filters, currentPage, pageSize],
    queryFn: () =>
      paymentMatchService.getPaymentMatches({
        ...filters,
        page: currentPage,
        per_page: pageSize,
      }),
  });

  // Fetch fund data when fund_id is present in filters
  const fundId = searchParams.get('fund_id');
  const { data: fundResponse } = useQuery({
    queryKey: ['fund', fundId],
    queryFn: () => fundService.getFund(fundId!),
    enabled: !!fundId,
  });

  // Fetch withdraw data when withdraw_id is present in filters
  const withdrawId = searchParams.get('withdraw_id');
  const { data: withdrawResponse } = useQuery({
    queryKey: ['withdraw', withdrawId],
    queryFn: () => withdrawService.getWithdraw(withdrawId!),
    enabled: !!withdrawId,
  });

  const paymentMatches = paymentMatchesResponse?.data?.paymentMatches || [];
  const pagination = paymentMatchesResponse?.data?.pagination || null;

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              Failed to load payment matches. Please try again.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Filter handlers
  const handleFilterChange = (key: keyof PaymentMatchFilters, value: any) => {
    setPendingFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const applyFilters = () => {
    setFilters(pendingFilters);
    setCurrentPage(1); // Reset to first page when applying filters
  };

  const clearFilters = () => {
    const clearedFilters = {};
    setPendingFilters(clearedFilters);
    setFilters(clearedFilters);
    setCurrentPage(1);
    // Navigate to base URL to remove fund_id/withdraw_id query parameters
    navigate('/transactions/payment-matches');
  };

  const hasFilterChanges = () => {
    return JSON.stringify(filters) !== JSON.stringify(pendingFilters);
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const handleViewDetails = (matchId: string) => {
    navigate(`/transactions/payment-matches/${matchId}`);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'matched':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getInitiatorBadgeColor = (initiator: string) => {
    return initiator === 'manual'
      ? 'bg-purple-100 text-purple-800'
      : 'bg-blue-100 text-blue-800';
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl font-bold">Payment Matches</h1>
            <p className="text-gray-600">Manage and monitor payment matches</p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Link to="/transactions/payment-matches-statistics">
              <Button
                variant="outline"
                className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-800 hover:bg-blue-100 hover:border-blue-300"
              >
                <BarChart className="w-4 h-4" />
                View Statistics
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="w-4 h-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>{' '}
        </div>

        {/* Fund Filter Indicator */}
        {fundId && fundResponse?.data && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="flex items-center gap-3">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-green-900">
                      Showing payment matches for Fund:{' '}
                      <span className="font-semibold">
                        {fundResponse.data.id}
                      </span>
                    </p>
                    <p className="text-xs text-green-700">
                      {formatCurrencyAmount(
                        fundResponse.data.amount,
                        fundResponse.data.currency
                      )}{' '}
                      • User:{' '}
                      {fundResponse.data.user?.full_name ||
                        fundResponse.data.user?.email}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-green-700 hover:text-green-900 hover:bg-green-100 self-start sm:self-center"
                >
                  <X className="h-4 w-4 mr-1" />
                  Show All Matches
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Withdraw Filter Indicator */}
        {withdrawId && withdrawResponse?.data && (
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="pt-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="flex items-center gap-3">
                  <TrendingDown className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="text-sm font-medium text-orange-900">
                      Showing payment matches for Withdrawal:{' '}
                      <span className="font-semibold">
                        {withdrawResponse.data.id}
                      </span>
                    </p>
                    <p className="text-xs text-orange-700">
                      {formatCurrencyAmount(
                        withdrawResponse.data.total_withdrawable_amount ||
                          withdrawResponse.data.base_withdrawable_amount,
                        withdrawResponse.data.fund?.currency || 'fiat'
                      )}{' '}
                      • User:{' '}
                      {withdrawResponse.data.user?.full_name ||
                        withdrawResponse.data.user?.email}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-orange-700 hover:text-orange-900 hover:bg-orange-100 self-start sm:self-center"
                >
                  <X className="h-4 w-4 mr-1" />
                  Show All Matches
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Filters</span>
                {hasFilterChanges() && (
                  <span className="ml-1 text-xs">(*)</span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Search
                  </label>
                  <Input
                    placeholder="Search by ID or user"
                    value={pendingFilters.search || ''}
                    onChange={e => handleFilterChange('search', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Status
                  </label>
                  <Select
                    value={pendingFilters.status || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'status',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All statuses</SelectItem>
                      <SelectItem value="matched">Matched</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Initiator
                  </label>
                  <Select
                    value={pendingFilters.initiator || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'initiator',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All initiators" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All initiators</SelectItem>
                      <SelectItem value="auto">Auto</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Currency
                  </label>
                  <Select
                    value={pendingFilters.currency || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'currency',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All currencies" />
                    </SelectTrigger>{' '}
                    <SelectContent>
                      <SelectItem value="all">All currencies</SelectItem>
                      <SelectItem value="fiat">Fiat (NGN)</SelectItem>
                      <SelectItem value="crypto">Crypto (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Min Amount
                  </label>
                  <Input
                    type="number"
                    placeholder="0.00"
                    value={pendingFilters.min_amount || ''}
                    onChange={e =>
                      handleFilterChange(
                        'min_amount',
                        e.target.value ? parseFloat(e.target.value) : undefined
                      )
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Max Amount
                  </label>
                  <Input
                    type="number"
                    placeholder="999999.99"
                    value={pendingFilters.max_amount || ''}
                    onChange={e =>
                      handleFilterChange(
                        'max_amount',
                        e.target.value ? parseFloat(e.target.value) : undefined
                      )
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Date From
                  </label>
                  <Input
                    type="date"
                    value={pendingFilters.date_from || ''}
                    onChange={e =>
                      handleFilterChange(
                        'date_from',
                        e.target.value || undefined
                      )
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Date To
                  </label>
                  <Input
                    type="date"
                    value={pendingFilters.date_to || ''}
                    onChange={e =>
                      handleFilterChange('date_to', e.target.value || undefined)
                    }
                  />
                </div>
              </div>

              {/* Buttons container - separate div below input fields */}
              <div className="flex justify-end gap-3 mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters {hasFilterChanges() && '(*)'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Payment Matches Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Payment Matches ({pagination?.total || 0})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Currency</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Initiator</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentMatches.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="text-gray-500">
                            <RefreshCw className="h-12 w-12 mx-auto mb-2 opacity-50" />
                            <p>No payment matches found</p>
                            <p className="text-sm">
                              Try adjusting your filters
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      paymentMatches.map(match => (
                        <TableRow key={match.id}>
                          <TableCell>
                            <span className="font-medium">
                              #{shortenId(match.id)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">
                              {formatCurrencyAmount(
                                match.amount,
                                (match.fund?.currency as Currency) || 'fiat'
                              )}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {match.fund?.currency?.toUpperCase() || 'N/A'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={getStatusBadgeColor(match.status)}
                            >
                              {match.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={getInitiatorBadgeColor(
                                match.initiator
                              )}
                            >
                              {match.initiator}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">
                              {formatDate(match.created_at)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewDetails(match.id)}
                              className="flex items-center gap-2"
                            >
                              <Eye className="h-4 w-4" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            <Pagination
              pagination={pagination}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              isLoading={isLoading}
              itemLabel="Matches"
            />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default PaymentMatchesPage;
