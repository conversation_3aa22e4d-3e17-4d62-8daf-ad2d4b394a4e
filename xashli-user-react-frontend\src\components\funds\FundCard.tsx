import {
  Calendar,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  ArrowDownToLine,
  Plus,
  Hourglass,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Badge } from '../ui';
import { Button } from '../ui/Button';
import type { Fund } from '../../types';
import { toNumber } from '../../utils/convert';

interface FundCardProps {
  fund: Fund;
  onView: () => void;
}

export function FundCard({ fund, onView }: FundCardProps) {
  const navigate = useNavigate();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-warning/20 text-warning border-warning/30';
      case 'matched':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'completed':
        return 'bg-success/20 text-success border-success/30';
      case 'cancelled':
        return 'bg-destructive/20 text-destructive border-destructive/30';
      default:
        return 'bg-muted/20 text-muted-foreground border-muted/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className='h-3 w-3' />;
      case 'matched':
        return <AlertCircle className='h-3 w-3' />;
      case 'completed':
        return <CheckCircle className='h-3 w-3' />;
      case 'cancelled':
        return <XCircle className='h-3 w-3' />;
      default:
        return null;
    }
  };
  const formatAmount = (amount: unknown, currency: string) => {
    // Convert to number or default to 0
    const num = Number(amount) || 0;

    // Format based on currency type
    return currency === 'fiat'
      ? `₦${Math.round(num).toLocaleString()}`
      : `${num.toFixed(4)} SOL`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isMatured = new Date(fund.maturity_date) <= new Date();

  // Enhanced withdrawal status logic
  const getWithdrawalStatus = () => {
    // Fund has been withdrawn
    if (fund.withdraw_id && fund.user_withdraw) {
      const withdrawStatus = fund.user_withdraw.status;
      switch (withdrawStatus) {
        case 'pending':
          return {
            type: 'withdrawal-pending',
            message: 'Withdrawal request submitted and pending',
            color: 'bg-warning/10 border-warning/30 text-warning',
            icon: <Clock className='h-3 w-3' />,
          };
        case 'matched':
          return {
            type: 'withdrawal-processing',
            message: 'Withdrawal in progress - payment matching',
            color: 'bg-primary/10 border-primary/30 text-primary',
            icon: <Hourglass className='h-3 w-3' />,
          };
        case 'completed':
          return {
            type: 'withdrawn',
            message: `Successfully withdrawn ${formatAmount(fund.user_withdraw.total_withdrawable_amount, fund.currency)}`,
            color: 'bg-success/10 border-success/30 text-success',
            icon: <CheckCircle className='h-3 w-3' />,
          };
        default:
          return {
            type: 'withdrawal-unknown',
            message: 'Withdrawal status unknown',
            color: 'bg-muted/10 border-muted/30 text-muted-foreground',
            icon: <AlertCircle className='h-3 w-3' />,
          };
      }
    }

    // Fund is cancelled
    if (fund.status === 'cancelled') {
      return {
        type: 'cancelled',
        message: 'Fund cancelled - no withdrawal available',
        color: 'bg-destructive/10 border-destructive/30 text-destructive',
        icon: <XCircle className='h-3 w-3' />,
      };
    }

    // Fund is not matured yet
    if (!isMatured) {
      const daysToMaturity = Math.ceil(
        (new Date(fund.maturity_date).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      );
      return {
        type: 'not-matured',
        message: `Matures in ${daysToMaturity} day${daysToMaturity !== 1 ? 's' : ''}`,
        color: 'bg-muted/10 border-muted/30 text-muted-foreground',
        icon: <Calendar className='h-3 w-3' />,
      };
    }

    // Fund is matured but not completed
    if (fund.status !== 'completed') {
      return {
        type: 'not-completed',
        message: 'Fund matured but not yet completed',
        color: 'bg-warning/10 border-warning/30 text-warning',
        icon: <AlertCircle className='h-3 w-3' />,
      };
    }

    // Fund is matured and completed but no recommitment
    if (!fund.next_fund_id) {
      return {
        type: 'ready-for-next-fund',
        message: 'Create recommitment fund to unlock withdrawal',
        color: 'bg-primary/10 border-primary/30 text-primary',
        icon: <Plus className='h-3 w-3' />,
      };
    }

    // Fund has recommitment - check if it's completed
    if (fund.next_fund) {
      if (fund.next_fund.status === 'completed') {
        return {
          type: 'ready-for-withdrawal',
          message: 'Withdrawal ready - your recommitment is completed',
          color: 'bg-success/10 border-success/30 text-success',
          icon: <CheckCircle className='h-3 w-3' />,
        };
      } else {
        return {
          type: 'ready-when-next-completes',
          message: `Recommitment ${fund.next_fund.status} (${formatAmount(fund.next_fund.amount, fund.next_fund.currency)}) - withdrawal available when completed`,
          color: 'bg-warning/10 border-warning/30 text-warning',
          icon: <Hourglass className='h-3 w-3' />,
        };
      }
    }

    // Fund has next_fund_id but recommitment data not loaded
    return {
      type: 'ready-when-next-completes',
      message: 'Recommitment processing - withdrawal available soon',
      color: 'bg-warning/10 border-warning/30 text-warning',
      icon: <Hourglass className='h-3 w-3' />,
    };
  };

  const withdrawalStatus = getWithdrawalStatus();

  const handleViewWithdrawal = () => {
    if (fund.user_withdraw?.id) {
      navigate(`/withdraws/${fund.user_withdraw.id}`);
    }
  };

  return (
    <div className='bg-background-secondary rounded-xl border border-border p-6 hover:border-primary/20 hover:shadow-lg transition-all duration-200'>
      {/* Header */}
      <div className='flex items-start justify-between mb-4'>
        <div className='flex items-center gap-3'>
          <div className='w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center'>
            <TrendingUp className='h-6 w-6 text-primary' />
          </div>{' '}
          <div>
            <h3 className='font-semibold text-foreground'>
              {formatAmount(fund.amount, fund.currency)}
            </h3>
            <p className='text-sm text-foreground-secondary'>
              {fund.currency === 'fiat' ? 'Fiat Fund' : 'Crypto Fund'}
            </p>
          </div>
        </div>
        <Badge
          variant='outline'
          className={`${getStatusColor(fund.status)} flex items-center gap-1`}
        >
          {getStatusIcon(fund.status)}
          {fund.status.charAt(0).toUpperCase() + fund.status.slice(1)}
        </Badge>
      </div>
      {/* Details */}{' '}
      <div className='space-y-3 mb-4'>
        {' '}
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Growth Rate</span>
          <span className='font-medium text-foreground'>
            {toNumber(fund.growth_percentage).toFixed(1)}%
          </span>
        </div>
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Expected Return</span>
          <span className='font-medium text-success'>
            {formatAmount(fund.growth_amount, fund.currency)}
          </span>
        </div>
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Maturity Date</span>
          <span
            className={`font-medium flex items-center gap-1 ${
              isMatured ? 'text-success' : 'text-foreground'
            }`}
          >
            <Calendar className='h-3 w-3' />
            {formatDate(fund.maturity_date)}
          </span>
        </div>
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Duration</span>
          <span className='font-medium text-foreground'>
            {fund.maturity_days} days
          </span>
        </div>
      </div>
      {/* Actions */}
      <div className='flex gap-2 pt-4 border-t border-border'>
        <Button variant='outline' size='sm' onClick={onView} className='flex-1'>
          View Details
        </Button>

        {(withdrawalStatus.type === 'withdrawn' ||
          withdrawalStatus.type === 'withdrawal-pending' ||
          withdrawalStatus.type === 'withdrawal-processing') &&
          fund.user_withdraw && (
            <Button
              variant='outline'
              size='sm'
              onClick={handleViewWithdrawal}
              className='text-primary hover:text-primary/80 hover:bg-primary/10'
            >
              <ArrowDownToLine className='h-3 w-3 mr-1' />
              {withdrawalStatus.type === 'withdrawn'
                ? 'View Withdrawal'
                : 'Track Withdrawal'}
            </Button>
          )}
      </div>
      {/* Withdrawal Status */}
      <div className={`mt-3 p-3 border rounded-lg ${withdrawalStatus.color}`}>
        <div className='flex items-center gap-2'>
          {withdrawalStatus.icon}
          <p className='text-xs font-medium'>{withdrawalStatus.message}</p>
        </div>
      </div>
    </div>
  );
}
