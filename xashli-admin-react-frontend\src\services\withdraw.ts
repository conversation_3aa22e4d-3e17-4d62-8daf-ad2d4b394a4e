import { apiClient } from './api';
import type { ApiResponse } from '../types';
import type {
  Withdraw,
  GetWithdrawsRequest,
  WithdrawsResponse,
  WithdrawStats,
} from '../types/withdraw';

export const withdrawService = {
  // Get paginated withdraws with filters
  async getWithdraws(
    params: GetWithdrawsRequest = {}
  ): Promise<ApiResponse<WithdrawsResponse>> {
    return apiClient.get<WithdrawsResponse>('/withdraws', { params });
  },

  // Get single withdraw by ID
  async getWithdraw(id: string): Promise<ApiResponse<Withdraw>> {
    return apiClient.get<Withdraw>(`/withdraws/${id}`);
  },

  // Get withdraw statistics with optional filters
  async getWithdrawStats(
    params: {
      user_id?: string;
      email?: string;
      date_from?: string;
      date_to?: string;
    } = {}
  ): Promise<ApiResponse<WithdrawStats>> {
    return apiClient.get<WithdrawStats>('/withdraws/statistics', { params });
  },

  // Dispute payment for a payment match
  async disputePayment(
    matchId: string,
    reason: string
  ): Promise<ApiResponse<any>> {
    return apiClient.post<any>(`/payment-disputes/${matchId}`, { reason });
  },
};
