import { apiClient } from './api';
import type {
  PaymentDispute,
  DisputeFilters,
  PaginatedDisputes,
  DisputeStats,
  ResolveDisputeRequest,
  RejectDisputeRequest,
  ApiResponse,
} from '../types';

export const disputeService = {
  /**
   * Get all disputes with filtering and pagination
   */
  getDisputes: async (
    filters?: DisputeFilters
  ): Promise<ApiResponse<PaginatedDisputes>> => {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const queryString = params.toString();
    const url = queryString
      ? `/payment-disputes?${queryString}`
      : '/payment-disputes';

    return apiClient.get<PaginatedDisputes>(url);
  },

  /**
   * Get a specific dispute by ID
   */
  getDisputeById: async (id: string): Promise<ApiResponse<PaymentDispute>> => {
    return apiClient.get<PaymentDispute>(`/payment-disputes/${id}`);
  },

  /**
   * Resolve a dispute
   */
  resolveDispute: async (
    id: string,
    data: ResolveDisputeRequest
  ): Promise<ApiResponse<PaymentDispute>> => {
    return apiClient.post<PaymentDispute>(
      `/payment-disputes/${id}/resolve`,
      data
    );
  },

  /**
   * Reject a dispute
   */
  rejectDispute: async (
    id: string,
    data?: RejectDisputeRequest
  ): Promise<ApiResponse<PaymentDispute>> => {
    return apiClient.post<PaymentDispute>(
      `/payment-disputes/${id}/reject`,
      data
    );
  },

  /**
   * Get dispute statistics for dashboard
   */
  getDisputeStats: async (params?: {
    date_from?: string;
    date_to?: string;
    currency?: string;
  }): Promise<ApiResponse<DisputeStats>> => {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }

    const queryString = queryParams.toString();
    const url = queryString
      ? `/payment-disputes/statistics?${queryString}`
      : '/payment-disputes/statistics';

    return apiClient.get<DisputeStats>(url);
  },
};
