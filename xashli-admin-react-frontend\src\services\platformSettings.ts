import { apiClient } from './api';
import type {
  PlatformSetting,
  CreatePlatformSettingRequest,
  UpdatePlatformSettingRequest,
} from '../types/platformSettings';
import type { ApiResponse } from '../types';

export const platformSettingsService = {
  /**
   * Get all platform settings
   */
  getAll: async (): Promise<ApiResponse<PlatformSetting[]>> => {
    return apiClient.get<PlatformSetting[]>('/platform-settings');
  },

  /**
   * Get a specific platform setting by ID
   */
  getById: async (id: string): Promise<ApiResponse<PlatformSetting>> => {
    return apiClient.get<PlatformSetting>(`/platform-settings/${id}`);
  },

  /**
   * Create a new platform setting
   */
  create: async (
    data: CreatePlatformSettingRequest
  ): Promise<ApiResponse<PlatformSetting>> => {
    return apiClient.post<PlatformSetting>('/platform-settings', data);
  },

  /**
   * Update an existing platform setting
   */
  update: async (
    id: string,
    data: UpdatePlatformSettingRequest
  ): Promise<ApiResponse<PlatformSetting>> => {
    return apiClient.put<PlatformSetting>(`/platform-settings/${id}`, data);
  },

  /**
   * Delete a platform setting
   */
  delete: async (id: string): Promise<ApiResponse<null>> => {
    return apiClient.delete<null>(`/platform-settings/${id}`);
  },

  /**
   * Get platform setting by key
   */
  getByKey: async (key: string): Promise<ApiResponse<PlatformSetting>> => {
    const response =
      await apiClient.get<PlatformSetting[]>('/platform-settings');
    const setting = response.data?.find(s => s.key === key);

    if (setting) {
      return {
        status: 'success',
        message: 'Setting retrieved successfully',
        data: setting,
      };
    }

    return {
      status: 'error',
      message: 'Setting not found',
      data: null,
    };
  },
};
