import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Search,
  Filter,
  TrendingUp,
  Users,
  DollarSign,
  Eye,
  Calendar,
  RefreshCw,
} from 'lucide-react';
import { userService } from '@/services/user';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading';
import { MainLayout } from '@/components/layout/MainLayout';
import { formatCurrencyAmount, formatDate } from '@/utils/format';
import type {
  RefereeData,
  RefereesApiResponse,
  ReferralInfoApiResponse,
  BonusesApiResponse,
  ExtendedRefereeData,
  ReferralFilters,
} from '@/types/referral';

export default function ReferralsPage() {
  const { userId } = useParams<{ userId: string }>();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ReferralFilters>({
    date_from: undefined,
    date_to: undefined,
  });

  // Fetch referees data for a specific user (admins can specify user_id)
  const {
    data: refereesData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['referees', { userId }],
    queryFn: () => userService.getReferees(userId ? { userId } : undefined),
  });

  // Fetch referral bonuses for commission data
  const { data: bonusesData } = useQuery({
    queryKey: ['referral-bonuses', { userId }],
    queryFn: () =>
      userService.getReferralBonuses(userId ? { userId } : undefined),
  });

  // Fetch referral info (basic stats) - now supports querying specific users
  const { data: stats } = useQuery({
    queryKey: ['referral-info', { userId }],
    queryFn: () => userService.getReferralInfo(userId ? { userId } : undefined),
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled locally by filtering the allReferees array
    // No need for URL search params in this context
  };

  const handleFilterChange = (key: string, value: string | undefined) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  if (isLoading && !refereesData) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <LoadingSpinner size="lg" className="mx-auto mb-4" />
              <p className="text-gray-600">Loading referrals data...</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Process the API response
  const refereesResponse = refereesData?.data as
    | RefereesApiResponse
    | undefined;
  const bonusesResponse = bonusesData?.data as BonusesApiResponse | undefined;
  const bonuses = bonusesResponse || { bonuses: [], summary: {} };
  const statsData = stats?.data as ReferralInfoApiResponse | undefined;

  // Flatten all referees for table display with level information
  const allReferees: ExtendedRefereeData[] = refereesResponse
    ? [
        ...(refereesResponse.level1 || []).map((referee: RefereeData) => ({
          ...referee,
          level: 1,
        })),
        ...(refereesResponse.level2 || []).map((referee: RefereeData) => ({
          ...referee,
          level: 2,
        })),
        ...(refereesResponse.level3 || []).map((referee: RefereeData) => ({
          ...referee,
          level: 3,
        })),
      ]
    : [];

  // Filter referees based on search term and date filters
  const filteredReferees = allReferees.filter(referee => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        referee.full_name.toLowerCase().includes(searchLower) ||
        referee.email.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Date filters
    if (filters.date_from) {
      const refereeDate = new Date(referee.created_at);
      const fromDate = new Date(filters.date_from);
      if (refereeDate < fromDate) return false;
    }

    if (filters.date_to) {
      const refereeDate = new Date(referee.created_at);
      const toDate = new Date(filters.date_to);
      if (refereeDate > toDate) return false;
    }

    return true;
  });
  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {' '}
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Referral Management
              {statsData && (
                <span className="text-lg font-normal text-gray-600 ml-2">
                  - {statsData.target_user.full_name}
                </span>
              )}
            </h1>
            <p className="text-sm text-gray-600">
              {statsData
                ? `Viewing referrals for ${statsData.target_user.full_name} (${statsData.target_user.email})`
                : 'Track and manage user referrals and commissions'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>{' '}
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Referrals
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {refereesResponse?.counts?.total || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Active Referrals
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {allReferees.filter(ref => ref.is_active).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-brand-gold-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Commission
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrencyAmount(
                    (bonuses?.summary?.fiat?.total_referral_bonus_amount || 0) +
                      (bonuses?.summary?.crypto?.total_referral_bonus_amount ||
                        0),
                    'fiat'
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Level 1 Referrals
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {refereesResponse?.counts?.level1 || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Filters and Search */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Search
              </CardTitle>
            </CardHeader>{' '}
            <CardContent>
              {' '}
              <form onSubmit={handleSearch} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search referrals..."
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <Input
                    type="date"
                    placeholder="Date from"
                    value={filters.date_from || ''}
                    onChange={e =>
                      handleFilterChange(
                        'date_from',
                        e.target.value || undefined
                      )
                    }
                  />

                  <Input
                    type="date"
                    placeholder="Date to"
                    value={filters.date_to || ''}
                    onChange={e =>
                      handleFilterChange('date_to', e.target.value || undefined)
                    }
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Button type="submit" size="sm">
                    Apply Filters
                  </Button>{' '}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setFilters({
                        date_from: undefined,
                        date_to: undefined,
                      });
                    }}
                  >
                    Clear All
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>{' '}
          {/* Referrals Table */}
          <Card>
            <CardHeader>
              <CardTitle>Referral Records</CardTitle>
              <CardDescription>
                {filteredReferees.length !== allReferees.length
                  ? `Showing ${filteredReferees.length} of ${allReferees.length} referrals (filtered)`
                  : `Showing ${allReferees.length} referrals`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner size="md" />
                </div>
              ) : filteredReferees.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No referrals found
                  </h3>
                  <p className="text-gray-500">
                    {allReferees.length === 0
                      ? 'No referral data matches your current filters.'
                      : 'No referrals match your search criteria. Try adjusting your filters.'}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Level
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Referred User
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Email
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Date Joined
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Referee Count
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Status
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredReferees.map((referee: ExtendedRefereeData) => (
                        <tr
                          key={referee.id}
                          className="border-b border-gray-100 hover:bg-gray-50"
                        >
                          <td className="py-3 px-4">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                referee.level === 1
                                  ? 'bg-blue-100 text-blue-800'
                                  : referee.level === 2
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-purple-100 text-purple-800'
                              }`}
                            >
                              Level {referee.level}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <Link
                              to={`/users/${referee.id}`}
                              className="font-medium text-gray-900 hover:text-brand-gold-600"
                            >
                              {referee.full_name}
                            </Link>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">
                              {referee.email}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-900">
                              {formatDate(referee.created_at)}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-900">
                              {referee.referee_count}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            {referee.is_active ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                              </span>
                            )}
                          </td>
                          <td className="py-3 px-4">
                            <Link to={`/users/${referee.id}`}>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>{' '}
        {/* Sidebar */}
        <div className="space-y-6">
          {/* Current User Info */}
          {statsData && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Current User Info
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Name:</span>
                    <span className="text-sm font-medium">
                      {statsData.target_user.full_name}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Email:</span>
                    <span className="text-sm font-medium">
                      {statsData.target_user.email}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      Referral Code:
                    </span>
                    <span className="text-sm font-medium font-mono bg-gray-100 px-2 py-1 rounded">
                      {statsData.referral_code}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      Total Referees:
                    </span>
                    <span className="text-sm font-medium">
                      {statsData.referee_count}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link to="/users" className="block">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users
                </Button>
              </Link>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => refetch()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Data
              </Button>{' '}
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
