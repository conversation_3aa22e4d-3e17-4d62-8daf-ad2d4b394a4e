// Platform Fee Types
import type { PaginationMeta, Currency } from './common';

export interface PlatformFee {
  id: string;
  fund_id: string;
  admin_user_id: string;
  fee_withdraw_id: string;
  fund_amount: number;
  fee_amount: number;
  fee_percentage: number;
  currency: Currency;
  collected_at: string;
  metadata?: {
    trigger_match_id?: string;
    admin_payment_method_id?: string;
    fee_method?: string;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
  fund?: {
    id: string;
    user_id: string;
    amount: number;
    currency: Currency;
    platform_fee_percentage?: number;
    status: string;
    created_at: string;
    maturity_date?: string;
    user?: {
      id: string;
      full_name: string;
      email: string;
      phone?: string;
    };
    paymentMethod?: {
      id: string;
      type: string;
      bank_name?: string;
      account_number?: string;
      account_name?: string;
      wallet_address?: string;
    };
  };
  admin?: {
    id: string;
    full_name: string;
    email: string;
  };
  withdraw?: {
    id: string;
    status: string;
    created_at: string;
    updated_at?: string;
  };
}

export interface PlatformFeeStatistics {
  counts: {
    total: number;
    fiat: number;
    crypto: number;
  };
  amounts: {
    total: {
      fiat: number;
      crypto: number;
    };
  };
}

export interface PlatformFeeFilters {
  currency?: 'fiat' | 'crypto';
  start_date?: string;
  end_date?: string;
  page?: number;
  per_page?: number;
}

// Platform Fee Pagination
export interface PaginatedPlatformFees {
  platformFees: PlatformFee[];
  pagination: PaginationMeta;
}

// Legacy interface for backward compatibility (deprecated)
export interface LegacyPaginatedPlatformFees {
  data: PlatformFee[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from?: number;
  to?: number;
}
