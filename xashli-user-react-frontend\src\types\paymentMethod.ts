// Payment Method types matching <PERSON><PERSON> backend
export interface PaymentMethod {
  id: string;
  user_id: string;
  type: 'bank' | 'crypto';
  bank_name?: string;
  bank_code?: string;
  account_number?: string;
  account_name?: string;
  wallet_address?: string;
  crypto_network?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Bank {
  id: number;
  name: string;
  code: string;
  longcode?: string;
  gateway?: string;
  pay_with_bank?: boolean;
  active?: boolean;
  country?: string;
  currency?: string;
  type?: string;
  is_deleted?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreatePaymentMethodRequest {
  type: 'bank' | 'crypto';
  bank_name?: string;
  bank_code?: string;
  account_number?: string;
  account_name?: string;
  wallet_address?: string;
  crypto_network?: string;
  confirmation_code?: string;
}

export interface UpdatePaymentMethodRequest {
  bank_name?: string;
  bank_code?: string;
  account_number?: string;
  account_name?: string;
  wallet_address?: string;
  crypto_network?: string;
  status?: 'active' | 'inactive';
  confirmation_code?: string;
}

export interface DeletePaymentMethodRequest {
  confirmation_code: string;
}

export interface BankValidationRequest {
  account_number: string;
  bank_code: string;
}

export interface BankValidationResponse {
  account_name: string;
  account_number: string;
  bank_code: string;
}

export interface PaymentMethodFormData {
  type: 'bank' | 'crypto';
  // Bank fields
  bankName?: string;
  bankCode?: string;
  accountNumber?: string;
  accountName?: string;
  // Crypto fields
  walletAddress?: string;
  cryptoNetwork?: string;
}
