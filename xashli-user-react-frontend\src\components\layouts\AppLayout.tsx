import { type ReactNode } from 'react';
import { SidebarProvider, useSidebar } from '@/contexts';
import { Sidebar } from './Sidebar';
import { TopBar } from './TopBar';

interface AppLayoutProps {
  children: ReactNode;
}

function AppLayoutContent({ children }: AppLayoutProps) {
  const { isMobile } = useSidebar();

  return (
    <div className='h-screen bg-background flex overflow-hidden'>
      {/* Sidebar */}
      <Sidebar className={isMobile ? '' : 'w-64 flex-shrink-0'} />

      {/* Main content area */}
      <div className='flex-1 flex flex-col min-w-0 overflow-hidden'>
        {/* Top bar */}
        <TopBar />

        {/* Page content */}
        <main className='flex-1 p-6 overflow-y-auto'>{children}</main>
      </div>
    </div>
  );
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <SidebarProvider>
      <AppLayoutContent>{children}</AppLayoutContent>
    </SidebarProvider>
  );
}
