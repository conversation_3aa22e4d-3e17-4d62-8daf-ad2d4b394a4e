import type { Currency } from '../types/common';

/**
 * Format amounts for fiat and crypto currencies
 */
export function formatCurrencyAmount(
  amount: number | string | null | undefined,
  currency: Currency
): string {
  const num = Number(amount) || 0;

  return currency === 'fiat'
    ? `₦${Math.round(num).toLocaleString()}`
    : `${num.toFixed(4)} SOL`;
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatCompactNumber(num: number | null | undefined): string {
  // Handle null, undefined, or invalid number values
  const numericValue = typeof num === 'number' && !isNaN(num) ? num : 0;

  return new Intl.NumberFormat('en-US', {
    notation: 'compact',
    maximumFractionDigits: 1,
  }).format(numericValue);
}

/**
 * Format date to readable string
 */
export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date));
}

/**
 * Format date and time to readable string
 */
export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
}

/**
 * Get relative time string (e.g., "2 hours ago")
 */
export function getRelativeTime(date: string | Date): string {
  const rtf = new Intl.RelativeTimeFormat('en-US', { numeric: 'auto' });
  const now = new Date();
  const target = new Date(date);
  const diff = target.getTime() - now.getTime();

  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (Math.abs(days) > 0) return rtf.format(days, 'day');
  if (Math.abs(hours) > 0) return rtf.format(hours, 'hour');
  if (Math.abs(minutes) > 0) return rtf.format(minutes, 'minute');
  return rtf.format(seconds, 'second');
}

/**
 * Convert a string to title case (capitalize first letter of each word)
 * Handles underscores by replacing them with spaces
 */
export function toTitleCase(str: string): string {
  return str
    .replace('_', ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
