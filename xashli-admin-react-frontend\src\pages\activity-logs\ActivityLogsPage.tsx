import React, { useState, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { FileText, Filter } from 'lucide-react';
import { MainLayout } from '../../components/layout';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { ActivityLogsTable } from '../../components/activity-logs/ActivityLogsTable';
import { Pagination } from '../../components/ui/pagination';
import {
  useAdminActivityLogs,
  useActionTypes,
} from '../../hooks/useAdminActivityLogs';
import type { AdminActivityLogFiltersState, ActionType } from '../../types';

export const ActivityLogsPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [showFilters, setShowFilters] = useState(false);

  // Get filters from URL params
  const [filters, setFilters] = useState<AdminActivityLogFiltersState>({
    action_type: (searchParams.get('action_type') as ActionType) || '',
    admin_id: searchParams.get('admin_id') || '',
    target_user_id: searchParams.get('target_user_id') || '',
    start_date: searchParams.get('start_date') || '',
    end_date: searchParams.get('end_date') || '',
  });

  // Pending filters for temporary changes before applying
  const [pendingFilters, setPendingFilters] =
    useState<AdminActivityLogFiltersState>(filters);

  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get('page') || '1', 10)
  );
  const [pageSize, setPageSize] = useState(15);

  // Initialize pending filters with current filters when filters change
  React.useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  // Filter management functions
  const handleFilterChange = (
    key: keyof AdminActivityLogFiltersState,
    value: string | undefined
  ) => {
    setPendingFilters(prev => {
      const newFilters = { ...prev };
      if (value && value !== '') {
        (newFilters as any)[key] = value;
      } else {
        (newFilters as any)[key] = '';
      }
      return newFilters;
    });
  };

  const applyFilters = () => {
    setFilters(pendingFilters);
    setCurrentPage(1); // Reset to first page when filters change

    // Update URL params
    const params = new URLSearchParams();
    Object.entries(pendingFilters).forEach(([key, value]) => {
      if (value && value !== '') {
        params.set(key, value);
      }
    });
    setSearchParams(params);
  };

  const clearFilters = () => {
    const clearedFilters: AdminActivityLogFiltersState = {
      action_type: '',
      admin_id: '',
      target_user_id: '',
      start_date: '',
      end_date: '',
    };
    setPendingFilters(clearedFilters);
    setFilters(clearedFilters);
    setCurrentPage(1);
    setSearchParams(new URLSearchParams());
  };

  const hasFilterChanges = () => {
    return JSON.stringify(filters) !== JSON.stringify(pendingFilters);
  };

  // Build query filters
  const queryFilters = useMemo(() => {
    const query: any = { page: currentPage, per_page: pageSize };

    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '') {
        query[key] = value;
      }
    });

    return query;
  }, [filters, currentPage, pageSize]);

  // Fetch data
  const {
    data: logsResponse,
    isLoading,
    error,
    refetch,
  } = useAdminActivityLogs(queryFilters);
  const { data: actionTypesResponse } = useActionTypes();

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const params = new URLSearchParams(searchParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }
    setSearchParams(params);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
    const params = new URLSearchParams(searchParams);
    params.delete('page'); // Remove page param since we're going back to page 1
    setSearchParams(params);
  };

  const logs = logsResponse?.data?.adminActivityLogs || [];
  const pagination = logsResponse?.data?.pagination;
  const actionTypes = actionTypesResponse?.data || [];

  if (error) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">Failed to load activity logs</div>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Admin Activity Logs
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Monitor all administrative actions and system activities
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Total Logs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination?.total || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Today's Activities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {
                  logs.filter(log => {
                    const today = new Date().toISOString().split('T')[0];
                    return log.created_at.startsWith(today);
                  }).length
                }
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Active Admins
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {new Set(logs.map(log => log.admin_id)).size}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Action Types
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {new Set(logs.map(log => log.action_type)).size}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
              <CardDescription>
                Filter activity logs by various criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Action Type</label>
                  <Select
                    value={pendingFilters.action_type || undefined}
                    onValueChange={value =>
                      handleFilterChange('action_type', value as ActionType)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All actions" />
                    </SelectTrigger>
                    <SelectContent>
                      {actionTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <Input
                    type="date"
                    value={pendingFilters.start_date}
                    onChange={e =>
                      handleFilterChange('start_date', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">End Date</label>
                  <Input
                    type="date"
                    value={pendingFilters.end_date}
                    onChange={e =>
                      handleFilterChange('end_date', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Admin ID</label>
                  <Input
                    placeholder="Filter by admin ID"
                    value={pendingFilters.admin_id}
                    onChange={e =>
                      handleFilterChange('admin_id', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Target User ID</label>
                  <Input
                    placeholder="Filter by target user ID"
                    value={pendingFilters.target_user_id}
                    onChange={e =>
                      handleFilterChange('target_user_id', e.target.value)
                    }
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters {hasFilterChanges() && '(*)'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Activity Logs Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Activity Logs
            </CardTitle>
            <CardDescription>
              Detailed view of all administrative actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ActivityLogsTable logs={logs} isLoading={isLoading} />
          </CardContent>

          {/* Pagination */}
          {pagination && (
            <Pagination
              pagination={pagination}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              isLoading={isLoading}
              itemLabel="Activity Logs"
            />
          )}
        </Card>
      </div>
    </MainLayout>
  );
};
