<?php

use App\Http\Controllers\AdminActivityLogController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ConfirmationController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FundController;
use App\Http\Controllers\PaymentDisputeController;
use App\Http\Controllers\PaymentMatchController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\PlatformFeeController;
use App\Http\Controllers\PlatformSettingsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\WithdrawController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication Routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);

    Route::middleware('jwt.auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('me', [AuthController::class, 'me']);
    });
});

// Protected Routes
Route::middleware('jwt.auth')->group(function () {
    // Verification Routes
    Route::prefix('verification')->group(function () {
        Route::get('status', [VerificationController::class, 'getVerificationStatus']);
        Route::post('email/send', [VerificationController::class, 'sendEmailVerification']);
        Route::post('email/verify', [VerificationController::class, 'verifyEmail']);
    });

    // Dashboard Routes
    Route::get('/dashboard/user', [DashboardController::class, 'user']);

    // Payment Method Routes
    Route::prefix('payment-methods')->group(function () {
        Route::get('/', [PaymentMethodController::class, 'index']);
        Route::post('/', [PaymentMethodController::class, 'store']);
        Route::get('/{id}', [PaymentMethodController::class, 'show']);
        Route::put('/{id}', [PaymentMethodController::class, 'update']);
        Route::delete('/{id}', [PaymentMethodController::class, 'destroy']);

        // Bank-related endpoints
        Route::get('/banks/list', [PaymentMethodController::class, 'getBanks']);
        Route::post('/banks/validate', [PaymentMethodController::class, 'validateBankAccount']);
    });

    // Fund Routes
    Route::prefix('funds')->group(function () {
        Route::get('/', [FundController::class, 'index']);
        Route::get('/statistics', [FundController::class, 'statistics']);
        Route::post('/', [FundController::class, 'store']);
        Route::get('/{id}', [FundController::class, 'show']);
        Route::post('/{id}/cancel', [FundController::class, 'cancel']);
    });

    // Withdraw Routes
    Route::prefix('withdraws')->group(function () {
        Route::get('/', [WithdrawController::class, 'index']);
        Route::get('/statistics', [WithdrawController::class, 'statistics']);
        Route::post('/', [WithdrawController::class, 'store']);
        Route::get('/{id}', [WithdrawController::class, 'show']);
    });

    // Payment Dispute Routes (for all authenticated users)
    Route::prefix('payment-disputes')->group(function () {
        Route::get('/', [PaymentDisputeController::class, 'index']);
        Route::get('/{id}', [PaymentDisputeController::class, 'show']);
        Route::post('/{matchId}', [PaymentDisputeController::class, 'store']);
        Route::get('/match/{matchId}', [PaymentDisputeController::class, 'getByMatch']);
    });

    // Payment Match Routes (for all authenticated users)
    Route::prefix('payment-matches')->group(function () {
        Route::get('/', [PaymentMatchController::class, 'index']);
        Route::get('/statistics', [PaymentMatchController::class, 'statistics']);
        Route::get('/{id}', [PaymentMatchController::class, 'show']);
        Route::post('/{id}/confirm-payment-sent', [PaymentMatchController::class, 'confirmPaymentSent']);
        Route::post('/{id}/confirm-payment-received', [PaymentMatchController::class, 'confirmPaymentReceived']);
        Route::post('/{id}/upload-payment-proof', [PaymentMatchController::class, 'uploadPaymentProof']);
    });

    // Referral Routes
    Route::prefix('referrals')->group(function () {
        Route::get('/info', [ReferralController::class, 'info']);
        Route::get('/referees', [ReferralController::class, 'referees']);
        Route::get('/bonuses', [ReferralController::class, 'bonuses']);
    });

    // Profile management routes for authenticated users
    Route::prefix('/profile')->group(function () {
        Route::put('/', [ProfileController::class, 'update']);
        Route::post('/upload-image', [ProfileController::class, 'uploadProfileImage']);
    });

    // Confirmation Code Routes
    Route::prefix('confirmation')->group(function () {
        Route::post('/generate', [ConfirmationController::class, 'generate']);
        Route::post('/verify', [ConfirmationController::class, 'verify']);
    });

    // Admin Routes (middleware handles access restriction)
    Route::middleware('admin')->group(function () {
        // Dashboard
        Route::get('/dashboard/admin', [DashboardController::class, 'admin']);

        // User Management
        Route::apiResource('/users', UserController::class);

        // Platform Settings
        Route::apiResource('/platform-settings', PlatformSettingsController::class);

        // Payment Match Management (Admin specific actions)
        Route::prefix('payment-matches')->group(function () {
            Route::post('/manual-match', [PaymentMatchController::class, 'manualMatch']);
            Route::post('/auto-match', [PaymentMatchController::class, 'triggerAutoMatch']);
            Route::post('/process-timeouts', [PaymentMatchController::class, 'processTimeouts']);
            Route::get('/timeout-statistics', [PaymentMatchController::class, 'getTimeoutStatistics']);
        });

        // Payment Dispute Management (Admin Only)
        Route::prefix('payment-disputes')->group(function () {
            Route::get('/statistics', [PaymentDisputeController::class, 'statistics']);
            Route::post('/{id}/resolve', [PaymentDisputeController::class, 'resolve']);
            Route::post('/{id}/reject', [PaymentDisputeController::class, 'reject']);
        });

        // Platform Fees
        Route::prefix('platform-fees')->group(function () {
            Route::get('/', [PlatformFeeController::class, 'index']);
            Route::get('/statistics', [PlatformFeeController::class, 'statistics']);
            Route::get('/{id}', [PlatformFeeController::class, 'show']);
        });

        // Activity Logs
        Route::prefix('admin-activity-logs')->group(function () {
            Route::get('/', [AdminActivityLogController::class, 'index']);
            Route::get('/statistics', [AdminActivityLogController::class, 'statistics']);
            Route::get('/action-types', [AdminActivityLogController::class, 'actionTypes']);
            Route::get('/{id}', [AdminActivityLogController::class, 'show']);
        });
    });
});
