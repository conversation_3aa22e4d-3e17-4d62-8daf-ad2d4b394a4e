<?php

namespace App\Http\Controllers;

use App\Models\ConfirmationCode;
use App\Services\EmailService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ConfirmationController extends Controller
{
    use ApiResponse;

    protected EmailService $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    // Generate a new confirmation code
    public function generate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|string',
            'context' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->validationError($validator->errors());
        }

        $user = auth()->user();

        // Validate action and context
        $allowedActions = ConfirmationCode::ALLOWED_ACTIONS;
        $allowedContexts = ConfirmationCode::ALLOWED_CONTEXTS;
        if (! in_array($request->action, $allowedActions, true)) {
            return $this->error('Invalid action.', 422);
        }
        if (! in_array($request->context, $allowedContexts, true)) {
            return $this->error('Invalid context.', 422);
        }

        // Rate limiting: max 5 codes per user per action per hour
        $recentCount = ConfirmationCode::where('user_id', $user?->id)
            ->where('action', $request->action)
            ->where('created_at', '>=', now()->subHour())
            ->count();
        if ($recentCount >= 5) {
            return $this->error('Too many requests. Please try again later.', 429);
        }

        $code = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        $expiresAt = now()->addMinutes(10);

        $confirmationCode = ConfirmationCode::create([
            'user_id' => $user?->id,
            'code' => $code,
            'action' => $request->action,
            'context' => $request->context,
            'expires_at' => $expiresAt,
        ]);

        // Send code via email
        $this->emailService->sendConfirmationCode($user, $code, $request->action, $request->context);

        return $this->success([
            'message' => 'Confirmation code generated successfully',
            'expires_at' => $expiresAt,
        ], 'Confirmation code generated successfully');
    }

    // Verify a confirmation code
    public function verify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|string',
            'code' => 'required|string',
            'context' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->validationError($validator->errors());
        }

        // Validate action and context
        $allowedActions = ConfirmationCode::ALLOWED_ACTIONS;
        $allowedContexts = ConfirmationCode::ALLOWED_CONTEXTS;
        if (! in_array($request->action, $allowedActions, true)) {
            return $this->error('Invalid action.', 422);
        }
        if (! in_array($request->context, $allowedContexts, true)) {
            return $this->error('Invalid context.', 422);
        }

        $query = ConfirmationCode::where('action', $request->action)
            ->where('user_id', auth()?->id)
            ->where('code', $request->code)
            ->where('context', $request->context)
            ->whereNull('used_at')
            ->where('expires_at', '>', now());

        $confirmationCode = $query->first();
        if (! $confirmationCode) {
            return $this->error('Invalid or expired confirmation code.', 400);
        }

        $confirmationCode->used_at = now();
        $confirmationCode->save();

        return $this->success(null, 'Confirmation code verified.');
    }
}
